<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whitebye.angel.ai.mapper.MethodCallRelationMapper">

    <resultMap id="BaseResultMap" type="com.whitebye.angel.ai.common.entity.MethodCallRelationDO">
        <id column="id" property="id"/>
        <result column="caller_app_name" property="callerAppName"/>
        <result column="caller_package_name" property="callerPackageName"/>
        <result column="caller_class_name" property="callerClassName"/>
        <result column="caller_method_name" property="callerMethodName"/>
        <result column="callee_app_name" property="calleeAppName"/>
        <result column="callee_package_name" property="calleePackageName"/>
        <result column="callee_class_name" property="calleeClassName"/>
        <result column="callee_method_name" property="calleeMethodName"/>
        <result column="call_type" property="callType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, caller_app_name, caller_package_name, caller_class_name, caller_method_name,
        callee_app_name, callee_package_name, callee_class_name, callee_method_name, call_type,
        create_time, update_time, deleted
    </sql>

    <insert id="insert" parameterType="com.whitebye.angel.ai.common.entity.MethodCallRelationDO">
        INSERT INTO method_call_relation (
            caller_app_name, caller_package_name, caller_class_name, caller_method_name,
            callee_app_name, callee_package_name, callee_class_name, callee_method_name, call_type,
            create_time, update_time, deleted
        ) VALUES (
            #{callerAppName}, #{callerPackageName}, #{callerClassName}, #{callerMethodName},
            #{calleeAppName}, #{calleePackageName}, #{calleeClassName}, #{calleeMethodName}, #{callType},
            NOW(), NOW(), 0
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO method_call_relation (
            caller_app_name, caller_package_name, caller_class_name, caller_method_name,
            callee_app_name, callee_package_name, callee_class_name, callee_method_name, call_type,
            create_time, update_time, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.callerAppName}, #{item.callerPackageName}, #{item.callerClassName}, #{item.callerMethodName},
                #{item.calleeAppName}, #{item.calleePackageName}, #{item.calleeClassName}, #{item.calleeMethodName}, #{item.callType},
                NOW(), NOW(), 0
            )
        </foreach>
    </insert>

    <update id="fakeDelete">
        UPDATE method_call_relation
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchFakeDelete">
        UPDATE method_call_relation
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.whitebye.angel.ai.common.entity.MethodCallRelationDO">
        UPDATE method_call_relation
        <set>
            <if test="callerAppName != null">caller_app_name = #{callerAppName},</if>
            <if test="callerPackageName != null">caller_package_name = #{callerPackageName},</if>
            <if test="callerClassName != null">caller_class_name = #{callerClassName},</if>
            <if test="callerMethodName != null">caller_method_name = #{callerMethodName},</if>
            <if test="calleeAppName != null">callee_app_name = #{calleeAppName},</if>
            <if test="calleePackageName != null">callee_package_name = #{calleePackageName},</if>
            <if test="calleeClassName != null">callee_class_name = #{calleeClassName},</if>
            <if test="calleeMethodName != null">callee_method_name = #{calleeMethodName},</if>
            <if test="callType != null">call_type = #{callType},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE method_call_relation
            <set>
                <if test="item.callerAppName != null">caller_app_name = #{item.callerAppName},</if>
                <if test="item.callerPackageName != null">caller_package_name = #{item.callerPackageName},</if>
                <if test="item.callerClassName != null">caller_class_name = #{item.callerClassName},</if>
                <if test="item.callerMethodName != null">caller_method_name = #{item.callerMethodName},</if>
                <if test="item.calleeAppName != null">callee_app_name = #{item.calleeAppName},</if>
                <if test="item.calleePackageName != null">callee_package_name = #{item.calleePackageName},</if>
                <if test="item.calleeClassName != null">callee_class_name = #{item.calleeClassName},</if>
                <if test="item.calleeMethodName != null">callee_method_name = #{item.calleeMethodName},</if>
                <if test="item.callType != null">call_type = #{item.callType},</if>
                update_time = NOW()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.whitebye.angel.ai.common.query.MethodCallRelationQuery">
        SELECT
        <include refid="Base_Column_List"/>
        FROM method_call_relation
        WHERE deleted = 0
        <if test="callerAppName != null and callerAppName != ''">
            AND caller_app_name = #{callerAppName}
        </if>
        <if test="callerPackageName != null and callerPackageName != ''">
            AND caller_package_name = #{callerPackageName}
        </if>
        <if test="callerClassName != null and callerClassName != ''">
            AND caller_class_name = #{callerClassName}
        </if>
        <if test="callerMethodName != null and callerMethodName != ''">
            AND caller_method_name = #{callerMethodName}
        </if>
        <if test="calleeAppName != null and calleeAppName != ''">
            AND callee_app_name = #{calleeAppName}
        </if>
        <if test="calleePackageName != null and calleePackageName != ''">
            AND callee_package_name = #{calleePackageName}
        </if>
        <if test="calleeClassName != null and calleeClassName != ''">
            AND callee_class_name = #{calleeClassName}
        </if>
        <if test="calleeMethodName != null and calleeMethodName != ''">
            AND callee_method_name = #{calleeMethodName}
        </if>
        <if test="callType != null">
            AND call_type = #{callType}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
