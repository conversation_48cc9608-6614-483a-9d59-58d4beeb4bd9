<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whitebye.angel.ai.mapper.CodeSearchMapper">

    <resultMap id="BaseResultMap" type="com.whitebye.angel.ai.common.entity.CodeSearchDO">
        <id column="id" property="id"/>
        <result column="vector_info" property="vectorInfo"/>
        <result column="app_name" property="appName"/>
        <result column="package_name" property="packageName"/>
        <result column="class_name" property="className"/>
        <result column="method_name" property="methodName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, vector_info, app_name, package_name, class_name, method_name
    </sql>

    <insert id="insert" parameterType="com.whitebye.angel.ai.common.entity.CodeSearchDO">
        INSERT INTO code_search (
            vector_info, app_name, package_name, class_name, method_name
        ) VALUES (
            #{vectorInfo}, #{appName}, #{packageName}, #{className}, #{methodName}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO code_search (
            vector_info, app_name, package_name, class_name, method_name
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.vectorInfo}, #{item.appName}, #{item.packageName}, #{item.className}, #{item.methodName}
            )
        </foreach>
    </insert>

    <delete id="realDelete">
        DELETE FROM code_search
        WHERE id = #{id}
    </delete>

    <delete id="batchRealDelete">
        DELETE FROM code_search
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateById" parameterType="com.whitebye.angel.ai.common.entity.CodeSearchDO">
        UPDATE code_search
        <set>
            <if test="vectorInfo != null">vector_info = #{vectorInfo},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="methodName != null">method_name = #{methodName}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE code_search
            <set>
                <if test="item.vectorInfo != null">vector_info = #{item.vectorInfo},</if>
                <if test="item.appName != null">app_name = #{item.appName},</if>
                <if test="item.packageName != null">package_name = #{item.packageName},</if>
                <if test="item.className != null">class_name = #{item.className},</if>
                <if test="item.methodName != null">method_name = #{item.methodName}</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.whitebye.angel.ai.common.query.CodeSearchQuery">
        SELECT
        <include refid="Base_Column_List"/>
        FROM code_search
        WHERE 1=1
        <if test="appName != null and appName != ''">
            AND app_name = #{appName}
        </if>
        <if test="packageName != null and packageName != ''">
            AND package_name = #{packageName}
        </if>
        <if test="className != null and className != ''">
            AND class_name = #{className}
        </if>
        <if test="methodName != null and methodName != ''">
            AND method_name = #{methodName}
        </if>
        ORDER BY id DESC
    </select>

</mapper>
