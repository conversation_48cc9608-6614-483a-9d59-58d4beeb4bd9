<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whitebye.angel.ai.mapper.AppInfoMapper">

    <resultMap id="BaseResultMap" type="com.whitebye.angel.ai.common.entity.AppInfoDO">
        <id column="id" property="id"/>
        <result column="app_name" property="appName"/>
        <result column="code_repository_url" property="codeRepositoryUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_name, code_repository_url, create_time, update_time, deleted
    </sql>

    <insert id="insert" parameterType="com.whitebye.angel.ai.common.entity.AppInfoDO">
        INSERT INTO app_info (
            app_name, code_repository_url,
            create_time, update_time, deleted
        ) VALUES (
            #{appName}, #{codeRepositoryUrl},
            NOW(), NOW(), 0
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO app_info (
            app_name, code_repository_url,
            create_time, update_time, deleted
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.appName}, #{item.codeRepositoryUrl},
                NOW(), NOW(), 0
            )
        </foreach>
    </insert>

    <update id="fakeDelete">
        UPDATE app_info
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchFakeDelete">
        UPDATE app_info
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.whitebye.angel.ai.common.entity.AppInfoDO">
        UPDATE app_info
        <set>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="codeRepositoryUrl != null">code_repository_url = #{codeRepositoryUrl},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE app_info
            <set>
                <if test="item.appName != null">app_name = #{item.appName},</if>
                <if test="item.codeRepositoryUrl != null">code_repository_url = #{item.codeRepositoryUrl},</if>
                update_time = NOW()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.whitebye.angel.ai.common.query.AppInfoQuery">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_info
        WHERE deleted = 0
        <if test="appName != null and appName != ''">
            AND app_name = #{appName}
        </if>
        <if test="codeRepositoryUrl != null and codeRepositoryUrl != ''">
            AND code_repository_url = #{codeRepositoryUrl}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
