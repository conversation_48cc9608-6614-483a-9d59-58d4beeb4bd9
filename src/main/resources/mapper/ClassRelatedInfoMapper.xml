<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whitebye.angel.ai.mapper.ClassRelatedInfoMapper">

    <resultMap id="BaseResultMap" type="com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO">
        <id column="id" property="id"/>
        <result column="app_name" property="appName"/>
        <result column="package_name" property="packageName"/>
        <result column="class_name" property="className"/>
        <result column="related_code" property="relatedCode"/>
        <result column="code_length" property="codeLength"/>
        <result column="class_type" property="classType"/>
        <result column="parent_package_name" property="parentPackageName"/>
        <result column="parent_class_name" property="parentClassName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_name, package_name, class_name, related_code, code_length, class_type, 
        parent_package_name, parent_class_name, create_time, update_time, deleted
    </sql>

    <insert id="insert" parameterType="com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO">
        INSERT INTO class_related_info (
            app_name, package_name, class_name, related_code, code_length, class_type,
            parent_package_name, parent_class_name, create_time, update_time, deleted
        ) VALUES (
            #{appName}, #{packageName}, #{className}, #{relatedCode}, #{codeLength}, #{classType},
            #{parentPackageName}, #{parentClassName}, NOW(), NOW(), 0
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO class_related_info (
            app_name, package_name, class_name, related_code, code_length, class_type,
            parent_package_name, parent_class_name, create_time, update_time, deleted
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.appName}, #{item.packageName}, #{item.className}, #{item.relatedCode}, #{item.codeLength}, #{item.classType},
                #{item.parentPackageName}, #{item.parentClassName}, NOW(), NOW(), 0
            )
        </foreach>
    </insert>

    <update id="fakeDelete">
        UPDATE class_related_info
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchFakeDelete">
        UPDATE class_related_info
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO">
        UPDATE class_related_info
        <set>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="relatedCode != null">related_code = #{relatedCode},</if>
            <if test="codeLength != null">code_length = #{codeLength},</if>
            <if test="classType != null">class_type = #{classType},</if>
            <if test="parentPackageName != null">parent_package_name = #{parentPackageName},</if>
            <if test="parentClassName != null">parent_class_name = #{parentClassName},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE class_related_info
            <set>
                <if test="item.appName != null">app_name = #{item.appName},</if>
                <if test="item.packageName != null">package_name = #{item.packageName},</if>
                <if test="item.className != null">class_name = #{item.className},</if>
                <if test="item.relatedCode != null">related_code = #{item.relatedCode},</if>
                <if test="item.codeLength != null">code_length = #{item.codeLength},</if>
                <if test="item.classType != null">class_type = #{item.classType},</if>
                <if test="item.parentPackageName != null">parent_package_name = #{item.parentPackageName},</if>
                <if test="item.parentClassName != null">parent_class_name = #{item.parentClassName},</if>
                update_time = NOW()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.whitebye.angel.ai.common.query.ClassRelatedInfoQuery">
        SELECT
        <include refid="Base_Column_List"/>
        FROM class_related_info
        WHERE deleted = 0
        <if test="appName != null and appName != ''">
            AND app_name = #{appName}
        </if>
        <if test="packageName != null and packageName != ''">
            AND package_name = #{packageName}
        </if>
        <if test="className != null and className != ''">
            AND class_name = #{className}
        </if>
        <if test="classType != null">
            AND class_type = #{classType}
        </if>
        <if test="parentPackageName != null and parentPackageName != ''">
            AND parent_package_name = #{parentPackageName}
        </if>
        <if test="parentClassName != null and parentClassName != ''">
            AND parent_class_name = #{parentClassName}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
