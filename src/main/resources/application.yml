server:
  port: 8080

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: Aa68876523.
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.whitebye.angel.ai.common.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

git:
  username: peishan
  password: Aa68876523$

# 日志配置
logging:
  level:
    com.whitebye.angel.ai.mapper: OFF
    org.mybatis: OFF
    org.apache.ibatis: OFF
