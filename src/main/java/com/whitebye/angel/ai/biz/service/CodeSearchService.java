package com.whitebye.angel.ai.biz.service;

import com.whitebye.angel.ai.common.req.CodeInfoQueryReq;
import com.whitebye.angel.ai.common.req.CodeSubInfoQueryReq;
import com.whitebye.angel.ai.common.req.MethodCallRelationQueryReq;
import com.whitebye.angel.ai.common.rsp.CodeInfoQueryRsp;
import com.whitebye.angel.ai.common.rsp.CodeSubInfoQueryRsp;
import com.whitebye.angel.ai.common.rsp.MethodCallRelationQueryRsp;

import java.util.List;

public interface CodeSearchService {

    Boolean analyzeSearchCode(List<String> appNameList);

    Boolean initAnalyzeSearch(List<String> appNameList);

    CodeInfoQueryRsp codeInfoQuery(CodeInfoQueryReq req);

    CodeSubInfoQueryRsp codeSubInfoQuery(CodeSubInfoQueryReq req);

    MethodCallRelationQueryRsp methodCallRelationQuery(MethodCallRelationQueryReq req);

}
