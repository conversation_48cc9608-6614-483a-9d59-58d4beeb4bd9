package com.whitebye.angel.ai.biz.manager;

import com.whitebye.angel.ai.common.entity.SessionInfoDO;
import com.whitebye.angel.ai.common.query.SessionInfoQuery;

import java.util.List;

public interface SessionInfoManager {

    /**
     * 新增会话
     */
    Integer addSessionInfo(SessionInfoDO record);

    /**
     * 新增会话
     */
    Integer addSessionInfo(List<SessionInfoDO> recordList);

    /**
     * 删除会话
     */
    Integer removeSessionInfo(Long id);

    /**
     * 删除会话
     */
    Integer removeSessionInfo(List<Long> ids);

    /**
     * 修改会话
     */
    Integer modifySessionInfo(SessionInfoDO record);

    /**
     * 修改会话
     */
    Integer modifySessionInfo(List<SessionInfoDO> recordList);

    /**
     * 查询会话
     */
    List<SessionInfoDO> querySessionInfo(SessionInfoQuery query);

} 