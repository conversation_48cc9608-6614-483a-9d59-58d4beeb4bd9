package com.whitebye.angel.ai.biz.service.impl;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.EnumDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.body.TypeDeclaration;
import com.github.javaparser.ast.body.AnnotationDeclaration;
import com.github.javaparser.ast.expr.MethodCallExpr;
import com.github.javaparser.ast.expr.ObjectCreationExpr;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;
import com.whitebye.angel.ai.biz.service.CodeSearchService;
import com.whitebye.angel.ai.common.entity.AppInfoDO;
import com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO;
import com.whitebye.angel.ai.common.entity.CodeSearchDO;
import com.whitebye.angel.ai.common.entity.MethodCallRelationDO;
import com.whitebye.angel.ai.common.entity.MethodInfoDO;
import com.whitebye.angel.ai.common.query.AppInfoQuery;
import com.whitebye.angel.ai.common.query.ClassRelatedInfoQuery;
import com.whitebye.angel.ai.common.query.MethodCallRelationQuery;
import com.whitebye.angel.ai.common.query.MethodInfoQuery;
import com.whitebye.angel.ai.common.req.CodeInfoQueryReq;
import com.whitebye.angel.ai.common.req.CodeSubInfoQueryReq;
import com.whitebye.angel.ai.common.req.MethodCallRelationQueryReq;
import com.whitebye.angel.ai.common.rsp.CodeInfoQueryRsp;
import com.whitebye.angel.ai.common.rsp.CodeSubInfoQueryRsp;
import com.whitebye.angel.ai.common.rsp.MethodCallRelationQueryRsp;
import com.whitebye.angel.ai.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Arrays;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.security.MessageDigest;

@Slf4j
@Service
public class CodeSearchServiceImpl implements CodeSearchService {

    @Resource
    private AppInfoMapper appInfoMapper;

    @Resource
    private MethodInfoMapper methodInfoMapper;

    @Resource
    private ClassRelatedInfoMapper classRelatedInfoMapper;

    @Resource
    private MethodCallRelationMapper methodCallRelationMapper;

    @Resource
    private CodeSearchMapper codeSearchMapper;

    @Value("${git.username}")
    private String gitUsername;

    @Value("${git.password}")
    private String gitPassword;

    // 文件内容缓存，避免重复解析相同的文件
    private final Map<String, Boolean> processedFilesCache = new ConcurrentHashMap<>();

    @Override
    public Boolean analyzeSearchCode(List<String> appNameList) {
        if (CollectionUtils.isEmpty(appNameList)) {
            return Boolean.FALSE;
        }

        for (String appName : appNameList) {
            log.info("开始处理应用: {}", appName);

            // 清理上一个应用的缓存
            clearProcessedFilesCache();

            // 1. 根据appName查询app_info获取代码仓库地址
            AppInfoQuery query = new AppInfoQuery();
            query.setAppName(appName);
            List<AppInfoDO> appInfoList = appInfoMapper.selectByParam(query);
            if (CollectionUtils.isEmpty(appInfoList)) {
                log.warn("未找到应用信息: {}", appName);
                continue;
            }
            
            AppInfoDO appInfo = appInfoList.get(0);
            String codeRepositoryUrl = appInfo.getCodeRepositoryUrl();
            Long appId = appInfo.getId();
            
            // 2. 使用GIT工具拉取应用代码 - 每次全量克隆
            String localPath = System.getProperty("java.io.tmpdir") + "/angel-ai-search/" + appName;
            File localDir = new File(localPath);

            // 删除已存在的目录，确保每次都是全量克隆
            if (localDir.exists()) {
                deleteDir(localDir);
            }

            try {
                cloneRepository(codeRepositoryUrl, localDir);
            } catch (Exception e) {
                log.error("拉取代码仓库失败: {}", codeRepositoryUrl, e);
                continue;
            }
            
            // 3. 处理所有JAVA代码 - 使用并行处理和批量操作
            List<File> srcDirs = new ArrayList<>();
            findSrcMainJavaDirs(localDir, srcDirs);

            // 收集所有Java文件
            List<File> allJavaFiles = new ArrayList<>();
            for (File srcDir : srcDirs) {
                collectJavaFiles(srcDir, allJavaFiles);
            }

            log.info("应用 {} 共找到 {} 个Java文件", appName, allJavaFiles.size());

            // 使用并行处理和批量操作
            processJavaFilesInParallel(allJavaFiles, appId, appName);
            
            // 清理临时目录
            deleteDir(localDir);
        }
        
        return Boolean.TRUE;
    }

    @Override
    public Boolean initAnalyzeSearch(List<String> appNameList) {
        if (CollectionUtils.isEmpty(appNameList)) {
            return Boolean.FALSE;
        }

        for (String appName : appNameList) {
            log.info("开始初始化代码搜索数据，应用: {}", appName);

            try {
                // 1. 查询所有的method_info数据
                MethodInfoQuery methodQuery = new MethodInfoQuery();
                methodQuery.setAppName(appName);
                List<MethodInfoDO> methodInfoList = methodInfoMapper.selectByParam(methodQuery);

                // 2. 查询所有的class_related_info数据
                ClassRelatedInfoQuery classQuery = new ClassRelatedInfoQuery();
                classQuery.setAppName(appName);
                List<ClassRelatedInfoDO> classRelatedInfoList = classRelatedInfoMapper.selectByParam(classQuery);

                // 3. 将method_info数据转换并保存到code_search
                List<CodeSearchDO> codeSearchList = new ArrayList<>();

                // 处理方法信息
                for (MethodInfoDO methodInfo : methodInfoList) {
                    CodeSearchDO codeSearch = new CodeSearchDO();
                    codeSearch.setVectorInfo(methodInfo.getRelatedCode()); // vector_info表示实际代码信息
                    codeSearch.setAppName(methodInfo.getAppName());
                    codeSearch.setPackageName(methodInfo.getPackageName());
                    codeSearch.setClassName(methodInfo.getClassName());
                    codeSearch.setMethodName(methodInfo.getMethodName());
                    codeSearchList.add(codeSearch);
                }

                // 处理类相关信息
                for (ClassRelatedInfoDO classRelatedInfo : classRelatedInfoList) {
                    CodeSearchDO codeSearch = new CodeSearchDO();
                    codeSearch.setVectorInfo(classRelatedInfo.getRelatedCode()); // vector_info表示实际代码信息
                    codeSearch.setAppName(classRelatedInfo.getAppName());
                    codeSearch.setPackageName(classRelatedInfo.getPackageName());
                    codeSearch.setClassName(classRelatedInfo.getClassName());
                    codeSearch.setMethodName(null); // 类信息没有方法名
                    codeSearchList.add(codeSearch);
                }

                // 4. 批量保存到code_search表
                if (CollectionUtils.isNotEmpty(codeSearchList)) {
                    codeSearchMapper.batchInsert(codeSearchList);
                    log.info("应用 {} 初始化代码搜索数据完成，共处理 {} 条记录", appName, codeSearchList.size());
                } else {
                    log.warn("应用 {} 没有找到任何代码数据", appName);
                }

            } catch (Exception e) {
                log.error("初始化应用 {} 的代码搜索数据失败", appName, e);
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public CodeInfoQueryRsp codeInfoQuery(CodeInfoQueryReq req) {
        // 参数校验
        if (req == null) {
            log.error("查询请求参数为空");
            return new CodeInfoQueryRsp();
        }

        if (req.getClassName() == null || req.getClassName().trim().isEmpty()) {
            log.error("类名不能为空");
            return new CodeInfoQueryRsp();
        }

        log.info("开始查询代码信息， 包名: {}, 类名: {}，方法名列表: {}", req.getPackageName(), req.getClassName(), req.getMethodNameList());

        CodeInfoQueryRsp response = new CodeInfoQueryRsp();

        try {
            // 1. 查询类相关代码信息
            String classRelatedCode = queryClassRelatedCode(req.getPackageName(), req.getClassName());
            response.setClassRelatedCode(classRelatedCode);

            // 2. 查询方法相关代码信息
            List<String> methodRelatedCodeList = queryMethodRelatedCode(req.getPackageName(), req.getClassName(), req.getMethodNameList());
            response.setMethodRelatedCodeList(methodRelatedCodeList);

            log.info("代码信息查询完成，类代码长度: {}, 方法数量: {}",
                    classRelatedCode != null ? classRelatedCode.length() : 0,
                    methodRelatedCodeList != null ? methodRelatedCodeList.size() : 0);

        } catch (Exception e) {
            log.error("查询代码信息失败 包名: {}, 类名: {}", req.getPackageName(), req.getClassName(), e);
            // 返回空的响应对象，而不是抛出异常
            response.setClassRelatedCode(null);
            response.setMethodRelatedCodeList(new ArrayList<>());
        }

        return response;
    }

    @Override
    public CodeSubInfoQueryRsp codeSubInfoQuery(CodeSubInfoQueryReq req) {
        // 参数校验
        if (req == null) {
            log.error("查询请求参数为空");
            return new CodeSubInfoQueryRsp();
        }

        if (req.getParentClassName() == null || req.getParentClassName().trim().isEmpty()) {
            log.error("父类名不能为空");
            return new CodeSubInfoQueryRsp();
        }

        log.info("开始查询子类信息, 父包名: {}, 父类名: {}", req.getParentPackageName(), req.getParentClassName());

        CodeSubInfoQueryRsp response = new CodeSubInfoQueryRsp();

        try {
            // 查询所有子类的相关信息（不包括方法信息）
            List<String> subClassRelatedCodeList = querySubClassRelatedCode(req.getParentPackageName(), req.getParentClassName());
            response.setSubClassRelatedCodeList(subClassRelatedCodeList);

            log.info("子类信息查询完成, 父包名: {}, 父类名: {}, 子类数量: {}",
                    req.getParentPackageName(), req.getParentClassName(),
                    subClassRelatedCodeList != null ? subClassRelatedCodeList.size() : 0);

        } catch (Exception e) {
            log.error("查询子类信息失败, 父包名: {}, 父类名: {}", req.getParentPackageName(), req.getParentClassName(), e);
            // 返回空的响应对象，而不是抛出异常
            response.setSubClassRelatedCodeList(new ArrayList<>());
        }

        return response;
    }

    @Override
    public MethodCallRelationQueryRsp methodCallRelationQuery(MethodCallRelationQueryReq req) {
        // 参数校验
        if (req == null) {
            log.error("方法调用关系查询请求参数为空");
            return new MethodCallRelationQueryRsp();
        }

        log.info("开始查询方法调用关系, 调用方应用名: {}, 调用方包名: {}, 调用方类名: {}, 调用方方法名: {}, " +
                "被调用方应用名: {}, 被调用方包名: {}, 被调用方类名: {}, 被调用方方法名: {}",
                req.getCallerAppName(), req.getCallerPackageName(), req.getCallerClassName(), req.getCallerMethodName(),
                req.getCalleeAppName(), req.getCalleePackageName(), req.getCalleeClassName(), req.getCalleeMethodName());

        MethodCallRelationQueryRsp response = new MethodCallRelationQueryRsp();

        try {
            // 构建查询条件
            MethodCallRelationQuery query = new MethodCallRelationQuery();
            query.setCallerAppName(req.getCallerAppName());
            query.setCallerPackageName(req.getCallerPackageName());
            query.setCallerClassName(req.getCallerClassName());
            query.setCallerMethodName(req.getCallerMethodName());
            query.setCalleeAppName(req.getCalleeAppName());
            query.setCalleePackageName(req.getCalleePackageName());
            query.setCalleeClassName(req.getCalleeClassName());
            query.setCalleeMethodName(req.getCalleeMethodName());

            // 查询方法调用关系
            List<MethodCallRelationDO> methodCallRelationList = methodCallRelationMapper.selectByParam(query);
            response.setMethodCallRelationList(methodCallRelationList);

            log.info("方法调用关系查询完成, 查询到 {} 条记录",
                    methodCallRelationList != null ? methodCallRelationList.size() : 0);

        } catch (Exception e) {
            log.error("查询方法调用关系失败", e);
            // 返回空的响应对象，而不是抛出异常
            response.setMethodCallRelationList(new ArrayList<>());
        }

        return response;
    }

    private void processJavaFile(File javaFile, Long appId, String appName, List<MethodInfoDO> methodInfoBatch, List<ClassRelatedInfoDO> classInfoBatch, List<MethodCallRelationDO> methodCallBatch) {
        try {
            // 生成文件的缓存键（基于文件路径和修改时间）
            String cacheKey = generateFileCacheKey(javaFile);

            // 检查缓存，如果已处理过且文件未修改，则跳过
            if (processedFilesCache.containsKey(cacheKey)) {
                log.debug("跳过已处理的文件: {}", javaFile.getName());
                return;
            }

            try (FileInputStream fis = new FileInputStream(javaFile)) {
                JavaParser javaParser = new JavaParser();
                ParseResult<CompilationUnit> result = javaParser.parse(fis, StandardCharsets.UTF_8);

                if (!result.isSuccessful() || !result.getResult().isPresent()) {
                    log.error("JavaParser解析失败: {}", javaFile.getAbsolutePath());
                    return;
                }

                CompilationUnit cu = result.getResult().get();
                String packageName = cu.getPackageDeclaration().map(pd -> pd.getName().asString()).orElse("");

                // 获取package和import信息
                StringBuilder packageAndImports = new StringBuilder();
                if (cu.getPackageDeclaration().isPresent()) {
                    packageAndImports.append(cu.getPackageDeclaration().get().toString()).append("\n");
                }
                cu.getImports().forEach(importDecl ->
                    packageAndImports.append(importDecl.toString()).append("\n")
                );

                // 处理所有类型声明
                for (TypeDeclaration<?> typeDecl : cu.getTypes()) {
                    processTypeDeclaration(typeDecl, packageName, appId, appName, "", packageAndImports.toString(), methodInfoBatch, classInfoBatch, methodCallBatch);
                }

                // 处理成功后添加到缓存
                processedFilesCache.put(cacheKey, true);
            }

        } catch (Exception e) {
            log.error("处理Java文件失败: {}", javaFile.getAbsolutePath(), e);
        }
    }

    private void processTypeDeclaration(TypeDeclaration<?> typeDecl, String packageName, Long appId, String appName, String parentClassName, String packageAndImports, List<MethodInfoDO> methodInfoBatch, List<ClassRelatedInfoDO> classInfoBatch, List<MethodCallRelationDO> methodCallBatch) {
        String className = parentClassName.isEmpty() ? typeDecl.getNameAsString() : parentClassName + "." + typeDecl.getNameAsString();

        // 处理方法 - 添加到批量列表
        List<MethodDeclaration> methods = typeDecl.getMethods();
        for (MethodDeclaration method : methods) {
            MethodInfoDO methodInfo = createMethodInfo(method, packageName, className, appId, appName);
            if (methodInfo != null) {
                methodInfoBatch.add(methodInfo);
            }

            // 解析方法调用关系
            extractMethodCallRelations(method, packageName, className, appName, methodCallBatch);
        }

        // 获取除方法外的所有代码作为class_related_info（不包含内部类）
        String classRelatedCode = getClassRelatedCode(typeDecl);
        String fullClassCode = packageAndImports + "\n" + classRelatedCode;
        ClassRelatedInfoDO classInfo = createClassRelatedInfo(fullClassCode, packageName, className, appId, appName, typeDecl);
        if (classInfo != null) {
            classInfoBatch.add(classInfo);
        }

        // 处理内部类型（作为独立的类信息记录）
        AtomicInteger anonymousCounter = new AtomicInteger(1);
        typeDecl.getChildNodes().forEach(child -> {
            if (child instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration innerClass = (ClassOrInterfaceDeclaration) child;
                if (innerClass.getNameAsString().isEmpty()) {
                    // 匿名内部类：父类名.$序号
                    String anonymousClassName = "$" + anonymousCounter.getAndIncrement();
                    processTypeDeclaration(innerClass, packageName, appId, appName, className, packageAndImports, methodInfoBatch, classInfoBatch, methodCallBatch);
                } else {
                    // 普通内部类：使用内部类名作为parentClassName
                    processTypeDeclaration(innerClass, packageName, appId, appName, className, packageAndImports, methodInfoBatch, classInfoBatch, methodCallBatch);
                }
            } else if (child instanceof EnumDeclaration) {
                EnumDeclaration innerEnum = (EnumDeclaration) child;
                // 内部枚举：使用外部类名作为parentClassName
                processTypeDeclaration(innerEnum, packageName, appId, appName, className, packageAndImports, methodInfoBatch, classInfoBatch, methodCallBatch);
            } else if (child instanceof AnnotationDeclaration) {
                AnnotationDeclaration innerAnnotation = (AnnotationDeclaration) child;
                // 内部注解：使用外部类名作为parentClassName
                processTypeDeclaration(innerAnnotation, packageName, appId, appName, className, packageAndImports, methodInfoBatch, classInfoBatch, methodCallBatch);
            }
        });
    }

    private String getClassRelatedCode(TypeDeclaration<?> typeDecl) {
        StringBuilder classRelatedCode = new StringBuilder();

        // 1. 添加类声明行（包含注解、修饰符、类名、继承等）
        String classDeclaration = getClassDeclarationLine(typeDecl);
        classRelatedCode.append(classDeclaration).append("\n");

        // 2. 如果是枚举类，添加枚举常量
        if (typeDecl instanceof EnumDeclaration) {
            EnumDeclaration enumDecl = (EnumDeclaration) typeDecl;
            enumDecl.getEntries().forEach(entry -> {
                String entryCode = removeComments(entry.toString());
                classRelatedCode.append("    ").append(entryCode).append("\n");
            });
        }

        // 3. 添加字段声明（包括静态变量和实例变量）
        typeDecl.getFields().forEach(field -> {
            String fieldCode = removeComments(field.toString());
            classRelatedCode.append(fieldCode).append("\n");
        });

        // 4. 添加构造函数
        typeDecl.getConstructors().forEach(constructor -> {
            String constructorCode = removeComments(constructor.toString());
            classRelatedCode.append(constructorCode).append("\n");
        });

        // 5. 添加静态代码块和实例初始化块
        typeDecl.getChildNodes().forEach(child -> {
            if (child instanceof com.github.javaparser.ast.stmt.BlockStmt) {
                String blockCode = removeComments(child.toString());
                classRelatedCode.append(blockCode).append("\n");
            }
        });

        // 6. 添加类结束大括号
        classRelatedCode.append("}");

        return classRelatedCode.toString();
    }

    private String getClassDeclarationLine(TypeDeclaration<?> typeDecl) {
        StringBuilder declaration = new StringBuilder();
        
        // 添加注解
        typeDecl.getAnnotations().forEach(annotation -> {
            declaration.append(annotation.toString()).append("\n");
        });
        
        // 添加修饰符
        if (typeDecl.getModifiers().isNonEmpty()) {
            typeDecl.getModifiers().forEach(modifier -> {
                declaration.append(modifier.getKeyword().asString()).append(" ");
            });
        }
        
        // 添加类型关键字和名称
        if (typeDecl instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration classOrInterface = (ClassOrInterfaceDeclaration) typeDecl;
            declaration.append(classOrInterface.isInterface() ? "interface " : "class ");
            declaration.append(typeDecl.getNameAsString());
            
            // 添加泛型参数
            if (classOrInterface.getTypeParameters().isNonEmpty()) {
                declaration.append("<");
                classOrInterface.getTypeParameters().forEach(param -> {
                    declaration.append(param.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2); // 移除最后的", "
                declaration.append(">");
            }
            
            // 添加继承和实现
            if (classOrInterface.getExtendedTypes().isNonEmpty()) {
                declaration.append(" extends ");
                classOrInterface.getExtendedTypes().forEach(type -> {
                    declaration.append(type.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2);
            }
            
            if (classOrInterface.getImplementedTypes().isNonEmpty()) {
                declaration.append(" implements ");
                classOrInterface.getImplementedTypes().forEach(type -> {
                    declaration.append(type.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2);
            }
        } else if (typeDecl instanceof EnumDeclaration) {
            declaration.append("enum ").append(typeDecl.getNameAsString());
            EnumDeclaration enumDecl = (EnumDeclaration) typeDecl;
            if (enumDecl.getImplementedTypes().isNonEmpty()) {
                declaration.append(" implements ");
                enumDecl.getImplementedTypes().forEach(type -> {
                    declaration.append(type.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2);
            }
        } else if (typeDecl instanceof AnnotationDeclaration) {
            declaration.append("@interface ").append(typeDecl.getNameAsString());
        }
        
        declaration.append(" {");
        
        return declaration.toString();
    }

    private String removeComments(String code) {
        StringBuilder result = new StringBuilder();
        boolean inSingleLineComment = false;
        boolean inMultiLineComment = false;
        boolean inString = false;
        char prev = 0;
        
        for (int i = 0; i < code.length(); i++) {
            char current = code.charAt(i);
            char next = (i + 1 < code.length()) ? code.charAt(i + 1) : 0;
            
            // 处理字符串内容，字符串内的注释符号不处理
            if (!inSingleLineComment && !inMultiLineComment) {
                if (current == '"' && prev != '\\') {
                    inString = !inString;
                    result.append(current);
                    prev = current;
                    continue;
                }
            }
            
            if (inString) {
                result.append(current);
                prev = current;
                continue;
            }
            
            // 处理单行注释 //
            if (!inMultiLineComment && current == '/' && next == '/') {
                inSingleLineComment = true;
                i++; // 跳过下一个字符
                prev = current;
                continue;
            }
            
            // 处理多行注释开始 /*
            if (!inSingleLineComment && current == '/' && next == '*') {
                inMultiLineComment = true;
                i++; // 跳过下一个字符
                prev = current;
                continue;
            }
            
            // 处理多行注释结束 */
            if (inMultiLineComment && current == '*' && next == '/') {
                inMultiLineComment = false;
                i++; // 跳过下一个字符
                prev = current;
                continue;
            }
            
            // 处理单行注释结束（换行）
            if (inSingleLineComment && (current == '\n' || current == '\r')) {
                inSingleLineComment = false;
                result.append(current);
                prev = current;
                continue;
            }
            
            // 如果不在注释中，添加字符
            if (!inSingleLineComment && !inMultiLineComment) {
                result.append(current);
            }
            
            prev = current;
        }
        
        return result.toString();
    }

    private MethodInfoDO createMethodInfo(MethodDeclaration method, String packageName, String className, Long appId, String appName) {
        try {
            MethodInfoDO methodInfo = new MethodInfoDO();
            methodInfo.setAppName(appName);
            methodInfo.setPackageName(packageName);
            methodInfo.setClassName(className);
            methodInfo.setMethodName(method.getNameAsString());

            // 删除方法代码中的注释和空行
            String methodCode = method.toString();
            String codeWithoutComments = removeComments(methodCode);
            String codeWithoutEmptyLines = removeEmptyLines(codeWithoutComments);

            methodInfo.setRelatedCode(codeWithoutEmptyLines);
            methodInfo.setCodeLength(codeWithoutEmptyLines.length());

            return methodInfo;
        } catch (Exception e) {
            log.error("创建方法信息失败: {}.{}.{}", packageName, className, method.getNameAsString(), e);
            return null;
        }
    }

    private void batchInsertMethodInfo(List<MethodInfoDO> methodInfoList) {
        if (CollectionUtils.isEmpty(methodInfoList)) {
            return;
        }
        try {
            methodInfoMapper.batchInsert(methodInfoList);
            log.debug("批量保存方法信息成功，数量: {}", methodInfoList.size());
        } catch (Exception e) {
            log.error("批量保存方法信息失败，数量: {}", methodInfoList.size(), e);
        }
    }

    private ClassRelatedInfoDO createClassRelatedInfo(String classRelatedCode, String packageName, String className, Long appId, String appName, TypeDeclaration<?> typeDecl) {
        try {
            ClassRelatedInfoDO classRelatedInfo = new ClassRelatedInfoDO();
            classRelatedInfo.setAppName(appName);
            classRelatedInfo.setPackageName(packageName);
            classRelatedInfo.setClassName(className);

            // 删除空行
            String codeWithoutEmptyLines = removeEmptyLines(classRelatedCode);

            classRelatedInfo.setRelatedCode(codeWithoutEmptyLines);
            classRelatedInfo.setCodeLength(codeWithoutEmptyLines.length());

            // 根据实际类型设置classType：1=类，2=接口，3=枚举，4=注解
            if (typeDecl instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration classOrInterface = (ClassOrInterfaceDeclaration) typeDecl;
                classRelatedInfo.setClassType(classOrInterface.isInterface() ? 2 : 1);
            } else if (typeDecl instanceof EnumDeclaration) {
                classRelatedInfo.setClassType(3);
            } else if (typeDecl instanceof AnnotationDeclaration) {
                classRelatedInfo.setClassType(4);
            } else {
                classRelatedInfo.setClassType(1); // 默认为类
            }

            // 处理父类信息
            if (className.contains(".")) {
                // 内部类：设置外部类为父类
                setInnerClassParentInfo(classRelatedInfo, className, packageName);
            } else {
                // 普通类：解析真正的继承关系
                extractParentClassInfo(typeDecl, classRelatedInfo, packageName);
            }

            return classRelatedInfo;
        } catch (Exception e) {
            log.error("创建类相关信息失败: {}.{}", packageName, className, e);
            return null;
        }
    }

    private void batchInsertClassInfo(List<ClassRelatedInfoDO> classInfoList) {
        if (CollectionUtils.isEmpty(classInfoList)) {
            return;
        }
        try {
            classRelatedInfoMapper.batchInsert(classInfoList);
            log.debug("批量保存类相关信息成功，数量: {}", classInfoList.size());
        } catch (Exception e) {
            log.error("批量保存类相关信息失败，数量: {}", classInfoList.size(), e);
        }
    }

    private void batchInsertMethodCallRelation(List<MethodCallRelationDO> methodCallList) {
        if (CollectionUtils.isEmpty(methodCallList)) {
            return;
        }
        try {
            methodCallRelationMapper.batchInsert(methodCallList);
            log.debug("批量保存方法调用关系成功，数量: {}", methodCallList.size());
        } catch (Exception e) {
            log.error("批量保存方法调用关系失败，数量: {}", methodCallList.size(), e);
        }
    }

    private String removeEmptyLines(String code) {
        return Arrays.stream(code.split("\n"))
                .filter(line -> !line.trim().isEmpty())
                .collect(Collectors.joining("\n"));
    }

    // 复用CodeServiceImpl中的工具方法
    private void findSrcMainJavaDirs(File dir, List<File> result) {
        if (dir == null || !dir.exists()) return;
        if (dir.isDirectory() && dir.getName().equals("java") && dir.getParentFile() != null
            && dir.getParentFile().getName().equals("main")
            && dir.getParentFile().getParentFile() != null
            && dir.getParentFile().getParentFile().getName().equals("src")) {
            result.add(dir);
            return;
        }
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            findSrcMainJavaDirs(file, result);
        }
    }

    private void collectJavaFiles(File dir, List<File> javaFiles) {
        if (dir == null || !dir.exists()) return;
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            if (file.isDirectory()) {
                collectJavaFiles(file, javaFiles);
            } else if (file.getName().endsWith(".java")) {
                javaFiles.add(file);
            }
        }
    }

    private boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            File[] children = dir.listFiles();
            if (children != null) {
                for (File child : children) {
                    deleteDir(child);
                }
            }
        }
        return dir.delete();
    }

    /**
     * 并行处理Java文件，提高处理效率
     */
    private void processJavaFilesInParallel(List<File> javaFiles, Long appId, String appName) {
        final int BATCH_SIZE = 1000;
        final int PARALLEL_BATCH_SIZE = 100; // 每个并行批次处理的文件数

        // 使用并行流处理文件，但控制批次大小避免内存溢出
        for (int i = 0; i < javaFiles.size(); i += PARALLEL_BATCH_SIZE) {
            int endIndex = Math.min(i + PARALLEL_BATCH_SIZE, javaFiles.size());
            List<File> batch = javaFiles.subList(i, endIndex);

            log.info("处理文件批次 {}-{}/{}", i + 1, endIndex, javaFiles.size());

            // 为每个批次创建独立的容器
            List<MethodInfoDO> methodInfoBatch = new ArrayList<>();
            List<ClassRelatedInfoDO> classInfoBatch = new ArrayList<>();
            List<MethodCallRelationDO> methodCallBatch = new ArrayList<>();

            // 使用并行流处理当前批次的文件
            batch.parallelStream().forEach(javaFile -> {
                List<MethodInfoDO> localMethodBatch = new ArrayList<>();
                List<ClassRelatedInfoDO> localClassBatch = new ArrayList<>();
                List<MethodCallRelationDO> localMethodCallBatch = new ArrayList<>();

                processJavaFile(javaFile, appId, appName, localMethodBatch, localClassBatch, localMethodCallBatch);

                // 同步添加到主批次容器
                synchronized (methodInfoBatch) {
                    methodInfoBatch.addAll(localMethodBatch);
                }
                synchronized (classInfoBatch) {
                    classInfoBatch.addAll(localClassBatch);
                }
                synchronized (methodCallBatch) {
                    methodCallBatch.addAll(localMethodCallBatch);
                }
            });

            // 批量插入当前批次的数据
            if (!methodInfoBatch.isEmpty()) {
                // 如果数据量太大，分批插入
                for (int j = 0; j < methodInfoBatch.size(); j += BATCH_SIZE) {
                    int methodEndIndex = Math.min(j + BATCH_SIZE, methodInfoBatch.size());
                    List<MethodInfoDO> subBatch = methodInfoBatch.subList(j, methodEndIndex);
                    batchInsertMethodInfo(subBatch);
                }
            }

            if (!classInfoBatch.isEmpty()) {
                // 如果数据量太大，分批插入
                for (int j = 0; j < classInfoBatch.size(); j += BATCH_SIZE) {
                    int classEndIndex = Math.min(j + BATCH_SIZE, classInfoBatch.size());
                    List<ClassRelatedInfoDO> subBatch = classInfoBatch.subList(j, classEndIndex);
                    batchInsertClassInfo(subBatch);
                }
            }

            if (!methodCallBatch.isEmpty()) {
                // 如果数据量太大，分批插入
                for (int j = 0; j < methodCallBatch.size(); j += BATCH_SIZE) {
                    int callEndIndex = Math.min(j + BATCH_SIZE, methodCallBatch.size());
                    List<MethodCallRelationDO> subBatch = methodCallBatch.subList(j, callEndIndex);
                    batchInsertMethodCallRelation(subBatch);
                }
            }

            log.info("批次处理完成，方法数: {}, 类数: {}, 方法调用关系数: {}",
                    methodInfoBatch.size(), classInfoBatch.size(), methodCallBatch.size());
        }
    }

    /**
     * Git克隆操作，全量拉取代码
     */
    private void cloneRepository(String repositoryUrl, File localDir) throws Exception {
        log.info("开始克隆仓库: {}", repositoryUrl);
        long startTime = System.currentTimeMillis();

        try {
            // 尝试克隆master分支
            Git.cloneRepository()
                    .setURI(repositoryUrl)
                    .setDirectory(localDir)
                    .setBranch("master")
                    .setCredentialsProvider(new UsernamePasswordCredentialsProvider(gitUsername, gitPassword))
                    .call();
        } catch (Exception e) {
            // 如果指定master分支失败，尝试克隆默认分支
            log.warn("克隆master分支失败，尝试克隆默认分支: {}", e.getMessage());
            Git.cloneRepository()
                    .setURI(repositoryUrl)
                    .setDirectory(localDir)
                    .setCredentialsProvider(new UsernamePasswordCredentialsProvider(gitUsername, gitPassword))
                    .call();
        }

        long endTime = System.currentTimeMillis();
        log.info("克隆完成，耗时: {} ms", endTime - startTime);
    }

    /**
     * 生成文件缓存键，基于文件路径和修改时间
     */
    private String generateFileCacheKey(File file) {
        try {
            String filePath = file.getAbsolutePath();
            long lastModified = file.lastModified();
            long fileSize = file.length();

            // 使用文件路径、修改时间和大小生成唯一键
            String input = filePath + "_" + lastModified + "_" + fileSize;

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            // 如果生成缓存键失败，返回文件路径作为备用
            log.warn("生成文件缓存键失败: {}", file.getAbsolutePath(), e);
            return file.getAbsolutePath() + "_" + file.lastModified();
        }
    }

    /**
     * 清理缓存，在处理新应用前调用
     */
    private void clearProcessedFilesCache() {
        processedFilesCache.clear();
        log.debug("已清理文件处理缓存");
    }

    /**
     * 查询类相关代码信息
     */
    private String queryClassRelatedCode(String packageName, String className) {
        try {
            // 构建查询条件
            ClassRelatedInfoQuery query = new ClassRelatedInfoQuery();
            query.setPackageName(packageName);
            query.setClassName(className);

            // 查询类相关信息
            List<ClassRelatedInfoDO> classInfoList = classRelatedInfoMapper.selectByParam(query);

            if (CollectionUtils.isNotEmpty(classInfoList)) {
                // 返回第一个匹配的类代码（通常应该只有一个）
                ClassRelatedInfoDO classInfo = classInfoList.get(0);
                log.debug("找到类相关代码，包名: {}, 类名: {}, 代码长度: {}",
                        packageName, className, classInfo.getCodeLength());
                return classInfo.getRelatedCode();
            } else {
                log.warn("未找到类相关代码, 包名: {}, 类名: {}", packageName, className);
                return null;
            }
        } catch (Exception e) {
            log.error("查询类相关代码失败, 包名: {}, 类名: {}", packageName, className, e);
            return null;
        }
    }

    /**
     * 查询方法相关代码信息
     */
    private List<String> queryMethodRelatedCode(String packageName, String className, List<String> methodNameList) {
        List<String> methodCodeList = new ArrayList<>();

        try {
            MethodInfoQuery query = new MethodInfoQuery();
            query.setPackageName(packageName);
            query.setClassName(className);
            query.setMethodNameList(methodNameList);

            List<MethodInfoDO> methodInfoList = methodInfoMapper.selectByParam(query);

            if (CollectionUtils.isNotEmpty(methodInfoList)) {
                for (MethodInfoDO methodInfo : methodInfoList) {
                    methodCodeList.add(methodInfo.getRelatedCode());
                }
                log.debug("查询到方法代码, 包名: {}, 类名: {}, 查询方法数: {}, 找到方法数: {}",
                        packageName, className,
                        methodNameList != null ? methodNameList.size() : 0,
                        methodInfoList.size());
            } else {
                log.warn("未找到任何方法代码, 包名: {}, 类名: {}, 方法名列表: {}",
                        packageName, className, methodNameList);
            }
        } catch (Exception e) {
            log.error("查询方法相关代码失败, 包名: {}, 类名: {}", packageName, className, e);
        }

        return methodCodeList;
    }

    /**
     * 查询子类相关代码信息（不包括方法信息）
     */
    private List<String> querySubClassRelatedCode(String parentPackageName, String parentClassName) {
        List<String> subClassCodeList = new ArrayList<>();

        try {
            // 构建查询条件
            ClassRelatedInfoQuery query = new ClassRelatedInfoQuery();
            query.setParentPackageName(parentPackageName);
            query.setParentClassName(parentClassName);

            // 查询所有子类相关信息
            List<ClassRelatedInfoDO> subClassInfoList = classRelatedInfoMapper.selectByParam(query);

            if (CollectionUtils.isNotEmpty(subClassInfoList)) {
                for (ClassRelatedInfoDO classInfo : subClassInfoList) {
                    subClassCodeList.add(classInfo.getRelatedCode());
                }
                log.debug("查询到子类相关代码, 父包名: {}, 父类名: {}, 子类数量: {}",
                        parentPackageName, parentClassName, subClassInfoList.size());
            } else {
                log.warn("未找到子类相关代码, 父包名: {}, 父类名: {}",
                        parentPackageName, parentClassName);
            }
        } catch (Exception e) {
            log.error("查询子类相关代码失败, 父包名: {}, 父类名: {}",
                    parentPackageName, parentClassName, e);
        }

        return subClassCodeList;
    }

    /**
     * 提取父类信息
     */
    private void extractParentClassInfo(TypeDeclaration<?> typeDecl, ClassRelatedInfoDO classRelatedInfo, String currentPackageName) {
        try {
            if (typeDecl instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration classDecl = (ClassOrInterfaceDeclaration) typeDecl;

                // 处理继承的类（extends）
                if (classDecl.getExtendedTypes().isNonEmpty()) {
                    // 通常一个类只能继承一个父类
                    String parentClassFullName = classDecl.getExtendedTypes().get(0).toString();
                    setParentClassInfo(classRelatedInfo, parentClassFullName, currentPackageName);
                    log.debug("找到父类继承关系: {} extends {}", typeDecl.getNameAsString(), parentClassFullName);
                }

                // 如果没有显式继承，但不是接口，则可能继承自Object（通常不需要记录）
                // 这里可以根据需要决定是否记录Object作为父类

            } else if (typeDecl instanceof EnumDeclaration) {
                // 枚举类默认继承自java.lang.Enum，可以根据需要决定是否记录
                // EnumDeclaration enumDecl = (EnumDeclaration) typeDecl;
                // 这里可以设置 java.lang.Enum 作为父类
            }

        } catch (Exception e) {
            log.warn("提取父类信息失败: {}", typeDecl.getNameAsString(), e);
        }
    }

    /**
     * 设置父类信息
     */
    private void setParentClassInfo(ClassRelatedInfoDO classRelatedInfo, String parentClassFullName, String currentPackageName) {
        try {
            // 解析父类的包名和类名
            if (parentClassFullName.contains(".")) {
                // 完整的类名，如 com.example.BaseService
                int lastDotIndex = parentClassFullName.lastIndexOf(".");
                String parentPackageName = parentClassFullName.substring(0, lastDotIndex);
                String parentClassName = parentClassFullName.substring(lastDotIndex + 1);

                classRelatedInfo.setParentPackageName(parentPackageName);
                classRelatedInfo.setParentClassName(parentClassName);
            } else {
                // 简单类名，如 BaseService，假设在同一个包中
                classRelatedInfo.setParentPackageName(currentPackageName);
                classRelatedInfo.setParentClassName(parentClassFullName);
            }

            log.debug("设置父类信息: parentPackageName={}, parentClassName={}",
                    classRelatedInfo.getParentPackageName(), classRelatedInfo.getParentClassName());

        } catch (Exception e) {
            log.warn("设置父类信息失败: {}", parentClassFullName, e);
        }
    }

    /**
     * 设置内部类的父类信息（外部类作为父类）
     */
    private void setInnerClassParentInfo(ClassRelatedInfoDO classRelatedInfo, String className, String packageName) {
        try {
            // 对于内部类，className 格式为 "OuterClass.InnerClass" 或 "OuterClass$1"
            int lastDotIndex = className.lastIndexOf(".");
            if (lastDotIndex > 0) {
                String outerClassName = className.substring(0, lastDotIndex);

                // 设置外部类为父类
                classRelatedInfo.setParentPackageName(packageName);
                classRelatedInfo.setParentClassName(outerClassName);

                log.debug("设置内部类父类信息: innerClass={}, outerClass={}, package={}",
                        className, outerClassName, packageName);
            }
        } catch (Exception e) {
            log.warn("设置内部类父类信息失败: {}", className, e);
        }
    }

    /**
     * 提取方法调用关系
     */
    private void extractMethodCallRelations(MethodDeclaration method, String packageName, String className, String appName, List<MethodCallRelationDO> methodCallBatch) {
        try {
            String methodName = method.getNameAsString();

            // 使用访问者模式遍历方法体中的所有方法调用
            method.accept(new VoidVisitorAdapter<Void>() {
                @Override
                public void visit(MethodCallExpr methodCall, Void arg) {
                    super.visit(methodCall, arg);

                    try {
                        MethodCallRelationDO relation = new MethodCallRelationDO();
                        // 设置调用方信息（添加长度限制）
                        relation.setCallerAppName(truncateIfTooLong(appName, 128, "调用方应用名"));
                        relation.setCallerPackageName(truncateIfTooLong(packageName, 512, "调用方包名"));
                        relation.setCallerClassName(truncateIfTooLong(className, 256, "调用方类名"));
                        relation.setCallerMethodName(truncateIfTooLong(methodName, 256, "调用方方法名"));

                        // 获取被调用的方法信息
                        String calleeMethodName = methodCall.getNameAsString();
                        relation.setCalleeMethodName(truncateIfTooLong(calleeMethodName, 256, "被调用方方法名"));

                        // 尝试解析被调用方的类信息
                        if (methodCall.getScope().isPresent()) {
                            String scope = methodCall.getScope().get().toString();
                            parseCalleeClassInfo(scope, relation, packageName, appName);
                        } else {
                            // 没有scope，可能是同一个类的方法调用
                            relation.setCalleeAppName(truncateIfTooLong(appName, 128, "被调用方应用名"));
                            relation.setCalleePackageName(truncateIfTooLong(packageName, 512, "被调用方包名"));
                            relation.setCalleeClassName(truncateIfTooLong(className, 256, "被调用方类名"));
                        }

                        // 设置调用类型（默认为实例方法调用）
                        relation.setCallType(1);

                        methodCallBatch.add(relation);

                    } catch (Exception e) {
                        log.warn("解析方法调用失败: {}", methodCall.toString(), e);
                    }
                }

                @Override
                public void visit(ObjectCreationExpr objectCreation, Void arg) {
                    super.visit(objectCreation, arg);

                    try {
                        MethodCallRelationDO relation = new MethodCallRelationDO();
                        // 设置调用方信息（添加长度限制）
                        relation.setCallerAppName(truncateIfTooLong(appName, 128, "调用方应用名"));
                        relation.setCallerPackageName(truncateIfTooLong(packageName, 512, "调用方包名"));
                        relation.setCallerClassName(truncateIfTooLong(className, 256, "调用方类名"));
                        relation.setCallerMethodName(truncateIfTooLong(methodName, 256, "调用方方法名"));

                        // 构造方法调用
                        String calleeClassName = objectCreation.getTypeAsString();
                        relation.setCalleeMethodName("<init>"); // 构造方法
                        relation.setCalleeClassName(truncateIfTooLong(calleeClassName, 256, "被调用方类名"));

                        // 尝试解析包名和应用名
                        parseCalleePackageFromClassName(calleeClassName, relation, packageName, appName);

                        // 设置调用类型为构造方法调用
                        relation.setCallType(3);

                        methodCallBatch.add(relation);

                    } catch (Exception e) {
                        log.warn("解析构造方法调用失败: {}", objectCreation.toString(), e);
                    }
                }
            }, null);

        } catch (Exception e) {
            log.error("提取方法调用关系失败: {}.{}.{}", packageName, className, method.getNameAsString(), e);
        }
    }

    /**
     * 解析被调用方的类信息
     */
    private void parseCalleeClassInfo(String scope, MethodCallRelationDO relation, String currentPackageName, String currentAppName) {
        try {
            // 过滤掉明显不是类名的复杂表达式
            if (isComplexExpression(scope)) {
                // 对于复杂表达式，使用当前类的信息
                relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "当前包名"));
                relation.setCalleeClassName("ComplexExpression");
                relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "被调用方应用名"));
                return;
            }

            // 简单的类名解析逻辑
            if (scope.contains(".")) {
                // 检查是否是合法的包名.类名格式
                if (isValidPackageClassName(scope)) {
                    String[] parts = scope.split("\\.");
                    if (parts.length >= 2) {
                        // 假设最后一个是类名，前面的是包名
                        String className = parts[parts.length - 1];
                        StringBuilder packageName = new StringBuilder();
                        for (int i = 0; i < parts.length - 1; i++) {
                            if (i > 0) packageName.append(".");
                            packageName.append(parts[i]);
                        }
                        String calleePackageName = packageName.toString();

                        relation.setCalleePackageName(truncateIfTooLong(calleePackageName, 512, "被调用方包名"));
                        relation.setCalleeClassName(truncateIfTooLong(className, 256, "被调用方类名"));
                        relation.setCallType(2); // 静态方法调用

                        // 判断被调用方的应用名
                        relation.setCalleeAppName(determineCalleeAppName(calleePackageName, currentAppName));
                    } else {
                        // 单个类名
                        relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "当前包名"));
                        relation.setCalleeClassName(truncateIfTooLong(scope, 256, "被调用方类名"));
                        relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "被调用方应用名"));
                    }
                } else {
                    // 不是合法的包名.类名格式，当作变量处理
                    relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "当前包名"));
                    relation.setCalleeClassName("VariableCall");
                    relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "被调用方应用名"));
                }
            } else {
                // 简单的变量名或类名
                relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "当前包名"));
                relation.setCalleeClassName(truncateIfTooLong(scope, 256, "被调用方类名"));
                relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "被调用方应用名"));
            }
        } catch (Exception e) {
            log.warn("解析被调用方类信息失败: {}", scope, e);
            // 设置默认值
            relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "默认包名"));
            relation.setCalleeClassName("Unknown");
            relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "默认被调用方应用名"));
        }
    }

    /**
     * 从类名解析包名
     */
    private void parseCalleePackageFromClassName(String className, MethodCallRelationDO relation, String currentPackageName, String currentAppName) {
        try {
            // 过滤掉复杂表达式
            if (isComplexExpression(className)) {
                relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "当前包名"));
                relation.setCalleeClassName("ComplexExpression");
                relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "被调用方应用名"));
                return;
            }

            if (className.contains(".") && isValidPackageClassName(className)) {
                // 完整的类名
                int lastDotIndex = className.lastIndexOf(".");
                String packageName = className.substring(0, lastDotIndex);
                String simpleClassName = className.substring(lastDotIndex + 1);

                // 检查长度并截断
                packageName = truncateIfTooLong(packageName, 512, "被调用方包名");
                simpleClassName = truncateIfTooLong(simpleClassName, 256, "被调用方类名");

                relation.setCalleePackageName(packageName);
                relation.setCalleeClassName(simpleClassName);

                // 判断被调用方的应用名
                relation.setCalleeAppName(determineCalleeAppName(packageName, currentAppName));
            } else {
                // 简单类名，假设在当前包中
                relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "当前包名"));
                relation.setCalleeClassName(truncateIfTooLong(className, 256, "被调用方类名"));
                relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "被调用方应用名")); // 同一个应用
            }
        } catch (Exception e) {
            log.warn("解析包名失败: {}", className, e);
            relation.setCalleePackageName(truncateIfTooLong(currentPackageName, 512, "默认包名"));
            relation.setCalleeClassName("Unknown");
            relation.setCalleeAppName(truncateIfTooLong(currentAppName, 128, "默认被调用方应用名"));
        }
    }

    /**
     * 判断被调用方的应用名
     */
    private String determineCalleeAppName(String calleePackageName, String currentAppName) {
        try {
            // 如果包名为空或null，默认为当前应用
            if (calleePackageName == null || calleePackageName.trim().isEmpty()) {
                return truncateIfTooLong(currentAppName, 128, "被调用方应用名");
            }

            // 判断是否为当前应用的包
            if (isCurrentAppPackage(calleePackageName, currentAppName)) {
                return truncateIfTooLong(currentAppName, 128, "被调用方应用名");
            }

            // 判断是否为第三方库（如java.*, javax.*, org.springframework.*, com.baomidou.*等）
            if (isThirdPartyLibrary(calleePackageName)) {
                return "third-party"; // 第三方库统一标记
            }

            // 尝试从包名推断应用名
            String inferredAppName = inferAppNameFromPackage(calleePackageName);
            if (inferredAppName != null) {
                return truncateIfTooLong(inferredAppName, 128, "推断的被调用方应用名");
            }

            // 默认返回未知应用
            return "unknown";

        } catch (Exception e) {
            log.warn("判断被调用方应用名失败: {}", calleePackageName, e);
            return truncateIfTooLong(currentAppName, 128, "默认被调用方应用名");
        }
    }

    /**
     * 判断是否为当前应用的包
     */
    private boolean isCurrentAppPackage(String packageName, String currentAppName) {
        // 简单的判断逻辑：如果包名包含当前应用名相关的关键词
        String lowerPackageName = packageName.toLowerCase();
        String lowerAppName = currentAppName.toLowerCase().replace("-", "").replace("_", "");

        return lowerPackageName.contains(lowerAppName) ||
               lowerPackageName.startsWith("com.whitebye.angel"); // 根据实际项目调整
    }

    /**
     * 判断是否为第三方库
     */
    private boolean isThirdPartyLibrary(String packageName) {
        String lowerPackageName = packageName.toLowerCase();

        // 常见的第三方库包名前缀
        String[] thirdPartyPrefixes = {
            "java.", "javax.", "sun.", "com.sun.",
            "org.springframework.", "org.apache.", "org.slf4j.", "org.junit.",
            "com.baomidou.", "com.alibaba.", "com.fasterxml.", "com.google.",
            "io.swagger.", "lombok.", "org.mybatis.", "com.github.javaparser."
        };

        for (String prefix : thirdPartyPrefixes) {
            if (lowerPackageName.startsWith(prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从包名推断应用名
     */
    private String inferAppNameFromPackage(String packageName) {
        try {
            // 简单的推断逻辑：从包名中提取可能的应用名
            String[] parts = packageName.split("\\.");

            // 如果是标准的Java包名结构：com.company.project.module
            if (parts.length >= 3) {
                // 通常第三部分是项目名
                return parts[2];
            }

            return null;
        } catch (Exception e) {
            log.warn("从包名推断应用名失败: {}", packageName, e);
            return null;
        }
    }

    /**
     * 判断是否是复杂表达式（不是简单的类名或包名.类名）
     */
    private boolean isComplexExpression(String scope) {
        if (scope == null || scope.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含复杂表达式的特征
        return scope.contains("(") || scope.contains(")") ||
               scope.contains("{") || scope.contains("}") ||
               scope.contains("[") || scope.contains("]") ||
               scope.contains("->") || scope.contains("=") ||
               scope.contains(" ") || scope.contains("\n") ||
               scope.contains("\t") || scope.contains(";") ||
               scope.contains("\"") || scope.contains("'") ||
               scope.contains("+") || scope.contains("-") ||
               scope.contains("*") || scope.contains("/") ||
               scope.contains("?") || scope.contains(":") ||
               scope.contains("<") || scope.contains(">") ||
               scope.length() > 100; // 超过100个字符的通常是复杂表达式
    }

    /**
     * 判断是否是合法的包名.类名格式
     */
    private boolean isValidPackageClassName(String scope) {
        if (scope == null || scope.trim().isEmpty()) {
            return false;
        }

        // 简单的验证：只包含字母、数字、点号和下划线
        return scope.matches("^[a-zA-Z_][a-zA-Z0-9_.]*[a-zA-Z0-9_]$") &&
               !scope.startsWith(".") && !scope.endsWith(".") &&
               scope.length() <= 200; // 合理的包名长度限制
    }

    /**
     * 如果字符串长度超过限制则截断，并记录日志
     */
    private String truncateIfTooLong(String value, int maxLength, String fieldName) {
        if (value == null) {
            return null;
        }

        if (value.length() > maxLength) {
            log.warn("{}过长，原始长度: {}, 内容: {}", fieldName, value.length(), value);
            String truncated = value.substring(0, maxLength);
            log.warn("{}已截断为: {}", fieldName, truncated);
            return truncated;
        }

        return value;
    }
}
