package com.whitebye.angel.ai.controller;

import com.whitebye.angel.ai.biz.service.CodeSearchService;
import com.whitebye.angel.ai.common.BizResult;
import com.whitebye.angel.ai.common.req.CodeInfoQueryReq;
import com.whitebye.angel.ai.common.req.CodeSubInfoQueryReq;
import com.whitebye.angel.ai.common.req.MethodCallRelationQueryReq;
import com.whitebye.angel.ai.common.rsp.CodeInfoQueryRsp;
import com.whitebye.angel.ai.common.rsp.CodeSubInfoQueryRsp;
import com.whitebye.angel.ai.common.rsp.MethodCallRelationQueryRsp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
public class CodeSearchController {

    @Resource
    private CodeSearchService codeSearchService;

    @PostMapping(value = "/code/search/analyze")
    public BizResult<Boolean> analyzeSearchCode(@RequestBody List<String> appNameList) {
        return BizResult.success(codeSearchService.analyzeSearchCode(appNameList));
    }

    @PostMapping(value = "/init/code/search")
    public BizResult<Boolean> initAnalyzeSearch(@RequestBody List<String> appNameList) {
        return BizResult.success(codeSearchService.initAnalyzeSearch(appNameList));
    }

    @PostMapping(value = "/code/info/query")
    public BizResult<CodeInfoQueryRsp> codeInfoQuery(@Valid @RequestBody CodeInfoQueryReq req) {
        return BizResult.success(codeSearchService.codeInfoQuery(req));
    }

    @PostMapping(value = "/code/sub/info/query")
    public BizResult<CodeSubInfoQueryRsp> codeSubInfoQuery(@Valid @RequestBody CodeSubInfoQueryReq req) {
        return BizResult.success(codeSearchService.codeSubInfoQuery(req));
    }

    @PostMapping(value = "/method/call/relation/query")
    public BizResult<MethodCallRelationQueryRsp> methodCallRelationQuery(@Valid @RequestBody MethodCallRelationQueryReq req) {
        return BizResult.success(codeSearchService.methodCallRelationQuery(req));
    }

}
