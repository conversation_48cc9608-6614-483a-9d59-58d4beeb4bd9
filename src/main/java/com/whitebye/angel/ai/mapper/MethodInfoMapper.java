package com.whitebye.angel.ai.mapper;

import com.whitebye.angel.ai.common.entity.MethodInfoDO;
import com.whitebye.angel.ai.common.query.MethodInfoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MethodInfoMapper {

    int insert(MethodInfoDO record);

    int batchInsert(List<MethodInfoDO> list);

    int fakeDelete(@Param("id") Long id);

    int batchFakeDelete(@Param("ids") List<Long> ids);

    int updateById(MethodInfoDO record);

    int batchUpdateByIds(List<MethodInfoDO> list);

    List<MethodInfoDO> selectByParam(MethodInfoQuery query);
}
