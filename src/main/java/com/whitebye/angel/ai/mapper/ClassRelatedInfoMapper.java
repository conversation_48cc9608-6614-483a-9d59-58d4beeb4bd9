package com.whitebye.angel.ai.mapper;

import com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO;
import com.whitebye.angel.ai.common.query.ClassRelatedInfoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ClassRelatedInfoMapper {

    int insert(ClassRelatedInfoDO record);

    int batchInsert(List<ClassRelatedInfoDO> list);

    int fakeDelete(@Param("id") Long id);

    int batchFakeDelete(@Param("ids") List<Long> ids);

    int updateById(ClassRelatedInfoDO record);

    int batchUpdateByIds(List<ClassRelatedInfoDO> list);

    List<ClassRelatedInfoDO> selectByParam(ClassRelatedInfoQuery query);
}
