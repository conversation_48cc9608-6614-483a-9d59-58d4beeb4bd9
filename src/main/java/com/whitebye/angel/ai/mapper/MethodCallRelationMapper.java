package com.whitebye.angel.ai.mapper;

import com.whitebye.angel.ai.common.entity.MethodCallRelationDO;
import com.whitebye.angel.ai.common.query.MethodCallRelationQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 方法调用关系 Mapper
 */
@Mapper
public interface MethodCallRelationMapper {

    int insert(MethodCallRelationDO record);

    int batchInsert(List<MethodCallRelationDO> list);

    int fakeDelete(@Param("id") Long id);

    int batchFakeDelete(@Param("ids") List<Long> ids);

    int updateById(MethodCallRelationDO record);

    int batchUpdateByIds(List<MethodCallRelationDO> list);

    List<MethodCallRelationDO> selectByParam(MethodCallRelationQuery query);

}
