package com.whitebye.angel.ai.mapper;

import com.whitebye.angel.ai.common.entity.CodeSearchDO;
import com.whitebye.angel.ai.common.query.CodeSearchQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CodeSearchMapper {

    int insert(CodeSearchDO record);

    int batchInsert(List<CodeSearchDO> list);

    int realDelete(@Param("id") Long id);

    int batchRealDelete(@Param("ids") List<Long> ids);

    int updateById(CodeSearchDO record);

    int batchUpdateByIds(List<CodeSearchDO> list);

    List<CodeSearchDO> selectByParam(CodeSearchQuery query);
}
