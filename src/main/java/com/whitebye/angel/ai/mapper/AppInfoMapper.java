package com.whitebye.angel.ai.mapper;

import com.whitebye.angel.ai.common.entity.AppInfoDO;
import com.whitebye.angel.ai.common.query.AppInfoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppInfoMapper {

    int insert(AppInfoDO record);

    int batchInsert(List<AppInfoDO> list);

    int fakeDelete(@Param("id") Long id);

    int batchFakeDelete(@Param("ids") List<Long> ids);

    int updateById(AppInfoDO record);

    int batchUpdateByIds(List<AppInfoDO> list);

    List<AppInfoDO> selectByParam(AppInfoQuery query);
}
