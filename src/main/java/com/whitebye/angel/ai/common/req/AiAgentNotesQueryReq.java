package com.whitebye.angel.ai.common.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AiAgentNotesQueryReq implements Serializable {

    /**
     * 智能体ID
     */
    @NotNull(message = "智能体ID不能为空")
    private Long agentId;

    /**
     * 笔记标题
     */
    private String title;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 笔记分类
     */
    private String category;

    /**
     * 标签数组
     */
    private String tags;

}