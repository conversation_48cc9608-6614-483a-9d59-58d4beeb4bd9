package com.whitebye.angel.ai.common.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class QueryCodeInfoReq implements Serializable {

    private static final long serialVersionUID = -933459379481933851L;

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private Long appId;

    /**
     * 类ID
     */
    private Long classId;

    /**
     * 方法ID
     */
    private Long methodId;

}
