package com.whitebye.angel.ai.common;

import lombok.Data;

@Data
public class BizResult<T> {

    /**
     * 0表示成功，其他表示失败
     */
    private Integer code;

    /**
     * 描述信息
     */
    private String message;

    private static final int SUCCESS_CODE = 0;

    private static final String SUCCESS_MSG = "success";

    /**
     * 结果对象
     */
    private T data;

    private BizResult() {
    }

    private BizResult(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> BizResult<T> success(T t) {
        return new BizResult<T>(SUCCESS_CODE, SUCCESS_MSG, t);
    }

    public static <T> BizResult<T> fail(Integer code, String message) {
        return new BizResult<T>(code, message, null);
    }

}
