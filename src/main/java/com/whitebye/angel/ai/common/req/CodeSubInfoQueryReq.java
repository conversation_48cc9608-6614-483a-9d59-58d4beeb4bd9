package com.whitebye.angel.ai.common.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class CodeSubInfoQueryReq implements Serializable {

    private static final long serialVersionUID = -2847593618472951234L;

    /**
     * 父包名
     */
    private String parentPackageName;

    /**
     * 父类名
     */
    @NotEmpty(message = "父类名不能为空")
    private String parentClassName;

}
