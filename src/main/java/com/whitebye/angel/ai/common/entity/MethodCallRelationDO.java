package com.whitebye.angel.ai.common.entity;

import lombok.Data;

import java.util.Date;

/**
 * 方法调用关系表
 */
@Data
public class MethodCallRelationDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 调用方应用名
     */
    private String callerAppName;

    /**
     * 调用方包名
     */
    private String callerPackageName;

    /**
     * 调用方类名
     */
    private String callerClassName;

    /**
     * 调用方方法名
     */
    private String callerMethodName;

    /**
     * 被调用方应用名
     */
    private String calleeAppName;

    /**
     * 被调用方包名
     */
    private String calleePackageName;

    /**
     * 被调用方类名
     */
    private String calleeClassName;

    /**
     * 被调用方方法名
     */
    private String calleeMethodName;

    /**
     * 调用类型：1=实例方法调用，2=静态方法调用，3=构造方法调用
     */
    private Integer callType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

}
