package com.whitebye.angel.ai.common.req;

import java.util.Date;

public class SessionInfoModifyReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会话开始时间
     */
    private Date startTime;

    /**
     * 会话结束时间
     */
    private Date endTime;

    /**
     * 最近一次对话时间
     */
    private Date latestDialogTime;

    /**
     * 会话状态(0:失效,1:有效)
     */
    private Integer status;

    /**
     * 当前会话的上下文数据(JSON格式)
     */
    private String contextData;

}
