package com.whitebye.angel.ai.common.entity;

import lombok.Data;

import java.util.Date;

@Data
public class SessionInfoDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户的唯一标识符
     */
    private String userId;

    /**
     * 会话开始时间
     */
    private Date startTime;

    /**
     * 会话结束时间
     */
    private Date endTime;

    /**
     * 最近一次对话时间
     */
    private Date latestDialogTime;

    /**
     * 会话状态(0:失效,1:有效)
     */
    private Integer status;

    /**
     * 当前会话的上下文数据(JSON格式)
     */
    private String contextData;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0未删除 1已删除
     */
    private Integer deleted;

} 