package com.whitebye.angel.ai.common.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class CodeInfoQueryReq implements Serializable {

    private static final long serialVersionUID = 3636661861310753814L;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    @NotEmpty(message = "类名不能为空")
    private String className;

    /**
     * 方法名列表
     */
    private List<String> methodNameList;

}
