package com.whitebye.angel.ai.common.req;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AiAgentNotesModifyReq implements Serializable {

    /**
     * 主键
     */
    @NotNull(message = "笔记ID不能为空")
    private Long id;

    /**
     * 智能体ID
     */
    private Long agentId;

    /**
     * 智能体名称
     */
    private String agentName;

    /**
     * 笔记标题
     */
    private String title;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 笔记分类
     */
    private String category;

    /**
     * 标签数组
     */
    private String tags;

    /**
     * 优先级(0-99,优先级最高0,优先级最低99)
     */
    private Integer priority;
} 