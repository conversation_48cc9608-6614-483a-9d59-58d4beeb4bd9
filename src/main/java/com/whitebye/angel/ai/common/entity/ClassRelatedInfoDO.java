package com.whitebye.angel.ai.common.entity;

import lombok.Data;

import java.util.Date;

@Data
public class ClassRelatedInfoDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    private String className;

    /**
     * 关联代码
     */
    private String relatedCode;

    /**
     * 代码长度
     */
    private Integer codeLength;

    /**
     * 类型(1:类,2:接口,3:枚举,4:注解)
     */
    private Integer classType;

    /**
     * 父包名
     */
    private String parentPackageName;

    /**
     * 父类名
     */
    private String parentClassName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
}
