package com.whitebye.angel.ai.common.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

public class AppCodeUtil {

    private static final String AI_HOME = "D:/my_home/my_ai";

    // 定义每个文件的最大大小（1MB = 1024 * 1024 字节）
    private static final int MAX_FILE_SIZE = 5 * 1024 * 1024;

    private static final List<String> APP_NAME_LIST = Arrays.asList(
            "jinyun", "jiuhua", "tianmu", "emei", "tiantai",
            "taishan", "songshan", "goods", "wuzhi", "longhu",
            "putuox", "huoyan", "huaguo", "tianmen", "hr",
            "wms", "dms"
    );

    public static void main(String[] args) {
        for (String appName : APP_NAME_LIST) {
            // 指定输入文件夹路径和输出文件路径
            String inputFolderPath = AI_HOME + "/应用原始代码-20250615"; // 替换为你的输入文件夹路径
            String outputFilePath = AI_HOME + "/应用代码txt-20250615"; // 替换为你的输出文件路径

            try {
                aggregateTextFiles(inputFolderPath, outputFilePath, appName);
            } catch (IOException e) {
                System.err.println("发生错误: " + e.getMessage());
            }
        }
    }

    /**
     * 读取文件夹及其子文件夹中的所有文本文件内容，并将其汇总到一个指定的输出文件中。
     *
     * @param inputFolderPath 文件夹路径
     * @param outputFilePath  输出文件路径
     * @throws IOException 如果发生I/O错误
     */
    public static void aggregateTextFiles(String inputFolderPath, String outputFilePath, String appName) throws IOException {
        // 获取文件夹路径
        Path inputFolder = Paths.get(inputFolderPath + "/" + appName);
        if (!Files.isDirectory(inputFolder)) {
            throw new IllegalArgumentException("指定的路径不是一个文件夹: " + inputFolderPath);
        }

        // 使用Stream API递归获取文件夹及其子文件夹中的所有文本文件
        try (Stream<Path> paths = Files.walk(inputFolder)) { // Files.walk 会递归遍历
            StringBuilder content = new StringBuilder();

            paths
                    .filter(Files::isRegularFile) // 筛选出普通文件
                    .filter(path -> path.toString().endsWith(".java")) // 只处理.java文件
                    .forEach(path -> {
                        try {
                            content.append(readFileContent(path)).append("\n\n");
                        } catch (IOException e) {
                            System.err.println("无法读取文件: " + path + ". 错误: " + e.getMessage());
                        }
                    });

            if (content.length() > MAX_FILE_SIZE) {
                writeToFileV2(outputFilePath + "/" + appName, content.toString());
            } else {
                // 将所有文件内容写入到输出文件
                writeToFile(outputFilePath + "/" + appName + ".txt", content.toString());
            }

        }
    }

    /**
     * 读取文件的内容。
     *
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException 如果发生I/O错误
     */
    private static String readFileContent(Path filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 将内容写入到指定文件。
     *
     * @param filePath 文件路径
     * @param content  内容
     * @throws IOException 如果发生I/O错误
     */
    private static void writeToFile(String filePath, String content) throws IOException {
        Path outputPath = Paths.get(filePath);
        try (BufferedWriter writer = Files.newBufferedWriter(outputPath)) {
            writer.write(content);
            System.out.println("文件内容已成功汇总到: " + filePath);
        }
    }

    /**
     * 将内容写入到指定文件，确保每个文件不超过 1MB。
     *
     * @param filePath 文件路径前缀（例如 "output/part_"）
     * @param content  内容
     * @throws IOException 如果发生I/O错误
     */
    public static void writeToFileV2(String filePath, String content) throws IOException {
        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8); // 将内容转换为字节数组
        int contentLength = contentBytes.length; // 内容总字节数
        int fileIndex = 1; // 文件编号
        int offset = 0; // 当前处理的字节偏移量

        while (offset < contentLength) {
            // 计算当前文件的最大字节数
            int chunkSize = Math.min(MAX_FILE_SIZE, contentLength - offset);

            // 提取当前文件的内容
            String chunkContent = new String(contentBytes, offset, chunkSize, StandardCharsets.UTF_8);

            // 构造文件名
            String outputFilePath = filePath + "_" + fileIndex + ".txt";

            // 写入当前文件
            Path outputPath = Paths.get(outputFilePath);
            try (BufferedWriter writer = Files.newBufferedWriter(outputPath, StandardCharsets.UTF_8)) {
                writer.write(chunkContent);
                System.out.println("文件内容已成功汇总到: " + outputFilePath);
            }

            // 更新偏移量和文件编号
            offset += chunkSize;
            fileIndex++;
        }
    }

}
