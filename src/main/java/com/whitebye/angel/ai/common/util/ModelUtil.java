package com.whitebye.angel.ai.common.util;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.ChatCompletion;
import com.openai.models.ChatCompletionCreateParams;

public class ModelUtil {

    private static final String DASH_SCOPE_API_KEY = "sk-26a52b9274ca4270a7b3d53756e5a751";
    private static final String BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";

    public static void main(String[] args) {
        OpenAIClient client = OpenAIOkHttpClient.builder()
                .apiKey(DASH_SCOPE_API_KEY)
                .baseUrl(BASE_URL)
                .build();
        ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
                .addUserMessage(getPrompt())
                .model("qwen-plus")
                .build();
        ChatCompletion chatCompletion = client.chat().completions().create(params);
        System.out.println(chatCompletion.choices().get(0).message().content().orElse("无返回内容"));
    }

    private static String getPrompt() {
        return "解析这个JAVA类代码信息，输出JSON结果\n" +
                "\n" +
                "### 返回JSON结果案例 ###\n" +
                "[\n" +
                "\t{\n" +
                "\t\t\"packageName\" : \"so.dian.taishan.service.impl\",\n" +
                "\t\t\"className\" : \"CartBuyServiceImpl\",\n" +
                "\t\t\"methodName\" : \"cartTrail\",\n" +
                "\t\t\"invokePackageName\" : \"so.dian.taishan.service.impl\",\n" +
                "\t\t\"invokeClassName\" : \"CartBuyServiceImpl\",\n" +
                "\t\t\"invokeMethodName\" : \"getAndCheckGoods\",\n" +
                "\t\t\"invokeOrder\" : 1\n" +
                "\t},\n" +
                "\t{\n" +
                "\t\t\"packageName\" : \"so.dian.taishan.service.impl\",\n" +
                "\t\t\"className\" : \"CartBuyServiceImpl\",\n" +
                "\t\t\"methodName\" : \"cartTrail\",\n" +
                "\t\t\"invokePackageName\" : \"so.dian.taishan.domain.buyer.service\",\n" +
                "\t\t\"invokeClassName\" : \"BuyerDomainService\",\n" +
                "\t\t\"invokeMethodName\" : \"getBuyer\",\n" +
                "\t\t\"invokeOrder\" : 2\n" +
                "\t}\n" +
                "]\n" +
                "\n" +
                "### 参数解释 ###\n" +
                "packageName : 调用方包名\n" +
                "className : 调用方类名\n" +
                "methodName : 调用方方法名\n" +
                "invokePackageName : 被调用方包名\n" +
                "invokeClassName : 被调用方类名\n" +
                "invokeMethodName : 被调用方方法名\n" +
                "invokeOrder : 调用次序\n" +
                "\n" +
                "### 注意事项 ###\n" +
                "1.不用输出解析步骤，只输出最终标准化的JSON结果\n" +
                "2.不管方法是否是私有方法，都需要处理并输出JSON。\n" +
                "3.不管方法是否在类内部调用，都需要处理并输出JSON。\n" +
                "4.必须输出可以反序列化的JSON字符串\n" +
                "\n" +
                "### JAVA类代码信息 ###\n" +
                "package so.dian.taishan.service.impl;\n" +
                "\n" +
                "import com.google.common.collect.Lists;\n" +
                "import lombok.extern.slf4j.Slf4j;\n" +
                "import org.apache.commons.collections.CollectionUtils;\n" +
                "import org.springframework.stereotype.Service;\n" +
                "import so.dian.common.util.AssertTool;\n" +
                "import so.dian.fis.credit.dto.response.CreditCardInfoDTO;\n" +
                "import so.dian.fis.credit.dto.response.CreditPeriodRespDTO;\n" +
                "import so.dian.goods.client.pojo.enums.OnSalePageEnum;\n" +
                "import so.dian.goods.client.pojo.enums.promotion.PromotionStrategyEnum;\n" +
                "import so.dian.taishan.app.assembler.CartAssembler;\n" +
                "import so.dian.taishan.app.assembler.CouponAssembler;\n" +
                "import so.dian.taishan.app.pojo.request.CartItemReq;\n" +
                "import so.dian.taishan.app.pojo.request.CartSettleReq;\n" +
                "import so.dian.taishan.app.pojo.request.CartStageTrailReq;\n" +
                "import so.dian.taishan.app.pojo.request.CartTrailReq;\n" +
                "import so.dian.taishan.app.pojo.response.*;\n" +
                "import so.dian.taishan.constant.BizConst;\n" +
                "import so.dian.taishan.constant.BizErrorCodes;\n" +
                "import so.dian.taishan.domain.buyer.entity.Buyer;\n" +
                "import so.dian.taishan.domain.buyer.service.BuyerDomainService;\n" +
                "import so.dian.taishan.domain.coupon.entity.CouponInfo;\n" +
                "import so.dian.taishan.domain.coupon.service.CouponDomainService;\n" +
                "import so.dian.taishan.domain.goods.entity.Goods;\n" +
                "import so.dian.taishan.domain.goods.service.GoodsDomainService;\n" +
                "import so.dian.taishan.domain.price.entity.GoodsPriceItem;\n" +
                "import so.dian.taishan.domain.price.service.PriceDomainService;\n" +
                "import so.dian.taishan.gateway.remote.CreditGateway;\n" +
                "import so.dian.taishan.service.CartBuyService;\n" +
                "import so.dian.taishan.util.AmountUtil;\n" +
                "\n" +
                "import javax.annotation.Resource;\n" +
                "import java.math.BigDecimal;\n" +
                "import java.text.DecimalFormat;\n" +
                "import java.util.List;\n" +
                "import java.util.Map;\n" +
                "import java.util.Objects;\n" +
                "import java.util.function.Function;\n" +
                "import java.util.stream.Collectors;\n" +
                "\n" +
                "/**\n" +
                " * 〈购物车购买服务〉<p>\n" +
                " * 〈商品加购试算，商品结算〉\n" +
                " *\n" +
                " * <AUTHOR> +
                " * @date 2022/07/29\n" +
                " */\n" +
                "@Slf4j\n" +
                "@Service\n" +
                "public class CartBuyServiceImpl implements CartBuyService {\n" +
                "\n" +
                "    @Resource\n" +
                "    private GoodsDomainService goodsDomainService;\n" +
                "\n" +
                "    @Resource\n" +
                "    private PriceDomainService priceDomainService;\n" +
                "\n" +
                "    @Resource\n" +
                "    private BuyerDomainService buyerDomainService;\n" +
                "\n" +
                "    @Resource\n" +
                "    private CartAssembler cartAssembler;\n" +
                "\n" +
                "    @Resource\n" +
                "    private CreditGateway creditGateway;\n" +
                "\n" +
                "    @Resource\n" +
                "    private CouponDomainService couponDomainService;\n" +
                "\n" +
                "    @Resource\n" +
                "    private CouponAssembler couponAssembler;\n" +
                "\n" +
                "    @Override\n" +
                "    public CartTrailRsp cartTrail(CartTrailReq cartTrailReq) {\n" +
                "        //1. 获取在售商品信息，判断存在下架\n" +
                "        List<Goods> goodsList = getAndCheckGoods(cartTrailReq.getGoodsItems());\n" +
                "\n" +
                "        //2. 获取买家信息\n" +
                "        Buyer buyer = buyerDomainService.getBuyer(cartTrailReq.getAgentId(), cartTrailReq.getUserId(), cartTrailReq.getNickName(), null);\n" +
                "\n" +
                "        //3、计算商品价格（包括赠品）\n" +
                "        List<GoodsPriceItem> priceCalcReqs = cartTrailReq.getGoodsItems().stream()\n" +
                "                .map(e -> GoodsPriceItem.buildCalculateReq(e.getSkuId(), e.getQuantity()))\n" +
                "                .collect(Collectors.toList());\n" +
                "        List<GoodsPriceItem> goodsPriceItems = priceDomainService.calculate(priceCalcReqs, goodsList, buyer, cartTrailReq.getOrderPage());\n" +
                "\n" +
                "        //4、计算购物车总计，包括总金额，优惠，件数，赠品\n" +
                "        CartTrailRsp cartTrailRsp = new CartTrailRsp();\n" +
                "        cartTrailRsp.setTotalAmount(priceDomainService.sumTotalAmount(goodsPriceItems));\n" +
                "        cartTrailRsp.setSettleAmount(priceDomainService.sumSettleAmount(goodsPriceItems));\n" +
                "        cartTrailRsp.setDiscountAmount(cartTrailRsp.getTotalAmount().subtract(cartTrailRsp.getSettleAmount()));\n" +
                "        cartTrailRsp.setTotalQuantity(countTotalQuantity(goodsPriceItems));\n" +
                "        cartTrailRsp.setGoodsItems(getCartTrailItems(goodsPriceItems));\n" +
                "        cartTrailRsp.setGiftItems(getCartGiftItems(goodsPriceItems));\n" +
                "        return cartTrailRsp;\n" +
                "    }\n" +
                "\n" +
                "    /**\n" +
                "     * 获取在售商品信息，校验是否有商品下架\n" +
                "     */\n" +
                "    private List<Goods> getAndCheckGoods(List<CartItemReq> cartItems) {\n" +
                "        List<Integer> skuIds = cartItems.stream()\n" +
                "                .map(CartItemReq::getSkuId)\n" +
                "                .distinct()\n" +
                "                .collect(Collectors.toList());\n" +
                "        return goodsDomainService.queryAndCheckOnSaleGoods(skuIds);\n" +
                "    }\n" +
                "\n" +
                "    private int countTotalQuantity(List<GoodsPriceItem> goodsPriceItems) {\n" +
                "        return goodsPriceItems.stream()\n" +
                "                .mapToInt(GoodsPriceItem::getQuantity)\n" +
                "                .sum();\n" +
                "    }\n" +
                "\n" +
                "    @Override\n" +
                "    public CartSettleRsp cartSettle(CartSettleReq cartSettleReq) {\n" +
                "        //1. 获取在售商品信息，判断存在下架\n" +
                "        List<Goods> goodsList = getAndCheckGoods(cartSettleReq.getGoodsItems());\n" +
                "\n" +
                "        //2. 获取买家信息\n" +
                "        Buyer buyer = buyerDomainService.getBuyer(cartSettleReq.getAgentId(), cartSettleReq.getUserId(), cartSettleReq.getNickName(), null);\n" +
                "\n" +
                "        //3、计算商品价格（包括赠品）\n" +
                "        List<GoodsPriceItem> priceCalcReqs = cartSettleReq.getGoodsItems().stream()\n" +
                "                .map(e -> GoodsPriceItem.buildCalculateReq(e.getSkuId(), e.getQuantity()))\n" +
                "                .collect(Collectors.toList());\n" +
                "        List<GoodsPriceItem> goodsPriceItems = priceDomainService.calculate(priceCalcReqs, goodsList, buyer, cartSettleReq.getOrderPage());\n" +
                "\n" +
                "        //4、计算购物车总计，包括总金额，优惠，件数\n" +
                "        CartSettleRsp cartSettleRsp = new CartSettleRsp();\n" +
                "        cartSettleRsp.setTotalAmount(priceDomainService.sumTotalAmount(goodsPriceItems));\n" +
                "        cartSettleRsp.setSettleAmount(priceDomainService.sumSettleAmount(goodsPriceItems));\n" +
                "        cartSettleRsp.setDiscountAmount(cartSettleRsp.getTotalAmount().subtract(cartSettleRsp.getSettleAmount()));\n" +
                "        cartSettleRsp.setTotalQuantity(countTotalQuantity(goodsPriceItems));\n" +
                "\n" +
                "        //5、构造购物车结算商品项，包括赠品\n" +
                "        Map<Integer, Goods> idGoodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity()));\n" +
                "        cartSettleRsp.setGoodsItems(getCartSettleItems(goodsPriceItems, idGoodsMap));\n" +
                "        cartSettleRsp.setGiftItems(getCartGiftItems(goodsPriceItems));\n" +
                "\n" +
                "        // 6.校验购物车中的商品是否需要配宝\n" +
                "        Boolean needSupplyFlag = goodsDomainService.checkNeedSupply(goodsList);\n" +
                "        cartSettleRsp.setNeedSupplyFlag(needSupplyFlag);\n" +
                "\n" +
                "        // 7.查询持有的优惠券和匹配本订单可用的优惠券\n" +
                "        CouponInfo couponInfo = couponDomainService.listAndMatchCouponInfo(buyer, goodsPriceItems);\n" +
                "        cartSettleRsp.setCouponInfo(couponAssembler.buildCouponInfoRsp(couponInfo));\n" +
                "        cartSettleRsp.setAfterCouponSettleAmount(cartSettleRsp.getSettleAmount().subtract(couponInfo.matchedAmount()));\n" +
                "        cartSettleRsp.setStrategyInfoList(getStrategyInfoRsp(goodsPriceItems));\n" +
                "        return cartSettleRsp;\n" +
                "    }\n" +
                "\n" +
                "    @Override\n" +
                "    public CartStageTrialRsp cartStageTrail(CartStageTrailReq req) {\n" +
                "        Buyer buyer = buyerDomainService.getBuyer(req.getAgentId(), req.getUserId(), req.getNickName(), null);\n" +
                "\n" +
                "        Integer agentType = buyer.isAgent() ? 1 : 2;\n" +
                "        Integer stageCount = req.getStageCount();\n" +
                "        Integer orderPage = req.getOrderPage();\n" +
                "\n" +
                "        // 只查询最高分期期数，其余信息均为空\n" +
                "        if (stageCount == null || stageCount <= 0) {\n" +
                "            CreditPeriodRespDTO creditPeriodRespDTO = creditGateway.getMaxPeriod(req.getAgentId().longValue(), agentType);\n" +
                "            AssertTool.notNull(creditPeriodRespDTO, BizErrorCodes.CREDIT_MAX_PERIOD_NOT_EXIST);\n" +
                "            AssertTool.notNull(creditPeriodRespDTO.getMaxPeriod(), BizErrorCodes.CREDIT_MAX_PERIOD_NOT_EXIST);\n" +
                "            Integer maxPeriod = creditPeriodRespDTO.getMaxPeriod();\n" +
                "            if(maxPeriod != null && maxPeriod > 1 && Objects.equals(orderPage, OnSalePageEnum.PART_PRICE.getCode())){\n" +
                "                maxPeriod = 1;\n" +
                "            }\n" +
                "            CartStageTrialRsp cartStageTrialRsp = new CartStageTrialRsp();\n" +
                "            cartStageTrialRsp.setMaxStageCount(maxPeriod);\n" +
                "            return cartStageTrialRsp;\n" +
                "        }\n" +
                "\n" +
                "        AssertTool.notNull(req.getOrderAmount(), BizErrorCodes.PARAM_ERROR, \"订单金额不能为空\");\n" +
                "        AssertTool.notNull(req.getDownPaymentAmount(), BizErrorCodes.PARAM_ERROR, \"首付金额不能为空\");\n" +
                "        Long orderAmount = AmountUtil.yuanToFen(req.getOrderAmount());\n" +
                "        Long downPaymentAmount = AmountUtil.yuanToFen(req.getDownPaymentAmount());\n" +
                "        AssertTool.notNull(orderAmount, BizErrorCodes.PARAM_ERROR, \"订单金额不能为空\");\n" +
                "        AssertTool.notNull(downPaymentAmount, BizErrorCodes.PARAM_ERROR, \"首付金额不能为空\");\n" +
                "\n" +
                "        CreditCardInfoDTO creditCardInfoDTO = creditGateway.findCardInfo(req.getAgentId().longValue(), agentType, orderAmount, downPaymentAmount, stageCount);\n" +
                "        // 前端依赖这两个错误码去展示提示窗口\n" +
                "        AssertTool.notNull(creditCardInfoDTO, BizErrorCodes.NO_CREDIT);\n" +
                "        AssertTool.isTrue(creditCardInfoDTO.getStatus() != null && creditCardInfoDTO.getStatus() == 1, BizErrorCodes.CREDIT_FORBIT, creditForbidReason(creditCardInfoDTO.getSubStatus()));\n" +
                "\n" +
                "        CreditPeriodRespDTO creditPeriodRespDTO = creditGateway.getMaxPeriod(req.getAgentId().longValue(), agentType);\n" +
                "        AssertTool.notNull(creditPeriodRespDTO, BizErrorCodes.CREDIT_MAX_PERIOD_NOT_EXIST);\n" +
                "        AssertTool.notNull(creditPeriodRespDTO.getMaxPeriod(), BizErrorCodes.CREDIT_MAX_PERIOD_NOT_EXIST);\n" +
                "\n" +
                "        DecimalFormat decimalFormat = new DecimalFormat(\",###,##0.00\");\n" +
                "        CartStageTrialRsp cartStageTrialRsp = new CartStageTrialRsp();\n" +
                "        cartStageTrialRsp.setDownPaymentProportion(creditCardInfoDTO.getFirstPaymentProportion());\n" +
                "        cartStageTrialRsp.setCreditPayAmount(AmountUtil.fenToYuan(orderAmount - creditCardInfoDTO.getFirstPaymentAmount()));\n" +
                "        cartStageTrialRsp.setCreditAccountBalance(AmountUtil.fenToYuan(creditCardInfoDTO.getAvailableCreditLine()));\n" +
                "        cartStageTrialRsp.setStageAmount(AmountUtil.fenToYuan(creditCardInfoDTO.getPeriodAmount()));\n" +
                "        Integer maxPeriod = creditPeriodRespDTO.getMaxPeriod();\n" +
                "        if(maxPeriod != null && maxPeriod > 1 && Objects.equals(orderPage, OnSalePageEnum.PART_PRICE.getCode())){\n" +
                "            maxPeriod = 1;\n" +
                "            cartStageTrialRsp.setMaxNumOfInstallmentReason(BizConst.CREDIT_TEXT);\n" +
                "        }\n" +
                "        cartStageTrialRsp.setMaxStageCount(maxPeriod);\n" +
                "\n" +
                "        // 用于展示的字段\n" +
                "        cartStageTrialRsp.setDownPaymentProportionStr(decimalFormat.format(creditCardInfoDTO.getFirstPaymentProportion().multiply(new BigDecimal(100))) + \"%\");\n" +
                "        cartStageTrialRsp.setCreditPayAmountStr(decimalFormat.format(AmountUtil.fenToYuan(orderAmount - creditCardInfoDTO.getFirstPaymentAmount())));\n" +
                "        cartStageTrialRsp.setCreditAccountBalanceStr(decimalFormat.format(AmountUtil.fenToYuan(creditCardInfoDTO.getAvailableCreditLine())));\n" +
                "        cartStageTrialRsp.setStageAmountStr(decimalFormat.format(AmountUtil.fenToYuan(creditCardInfoDTO.getPeriodAmount())));\n" +
                "        return cartStageTrialRsp;\n" +
                "    }\n" +
                "\n" +
                "    private String creditForbidReason(Integer subStatus) {\n" +
                "        if (subStatus == null) {\n" +
                "            return null;\n" +
                "        }\n" +
                "\n" +
                "        // 授信账户子状态\n" +
                "        switch (subStatus) {\n" +
                "            case 1:\n" +
                "                return \"账户冻结\";\n" +
                "            case 2:\n" +
                "                return \"账户被标记不可用\";\n" +
                "            case 3:\n" +
                "                return \"账户存在支付中的订单\";\n" +
                "            case 4:\n" +
                "                return \"授信账户和授信系统订单存在退款状态不一致\";\n" +
                "        }\n" +
                "\n" +
                "        return null;\n" +
                "    }\n" +
                "\n" +
                "    private List<CartGiftItemDTO> getCartGiftItems(List<GoodsPriceItem> goodsPriceItems) {\n" +
                "        return goodsPriceItems.stream()\n" +
                "                .filter(GoodsPriceItem::hasGiftPromotion)\n" +
                "                .map(goodsPriceItem -> cartAssembler.buildCartGiftDTO(goodsPriceItem.getPromotionGift()))\n" +
                "                .collect(Collectors.toList());\n" +
                "    }\n" +
                "\n" +
                "    private List<CartSettleItemDTO> getCartSettleItems(List<GoodsPriceItem> goodsPriceItems, Map<Integer, Goods> idGoodsMap) {\n" +
                "        return goodsPriceItems.stream()\n" +
                "                .map(e -> cartAssembler.buildCartSettleItemDTO(e, idGoodsMap.get(e.getSkuId())))\n" +
                "                .collect(Collectors.toList());\n" +
                "    }\n" +
                "\n" +
                "    private List<CartTrailItemDTO> getCartTrailItems(List<GoodsPriceItem> goodsPriceItems) {\n" +
                "        return goodsPriceItems.stream()\n" +
                "                .map(e -> cartAssembler.buildCartTrailItemDTO(e))\n" +
                "                .collect(Collectors.toList());\n" +
                "    }\n" +
                "\n" +
                "    private List<StrategyInfoRsp> getStrategyInfoRsp(List<GoodsPriceItem> goodsPriceItems) {\n" +
                "        List<StrategyInfoRsp> result = Lists.newArrayList();\n" +
                "        if (CollectionUtils.isEmpty(goodsPriceItems)) {\n" +
                "            return result;\n" +
                "        }\n" +
                "\n" +
                "        Map<Integer, List<GoodsPriceItem>> strategyInfoMap = goodsPriceItems.stream().filter(GoodsPriceItem::hasPricePromotion).collect(Collectors.groupingBy(item -> item.getPromotionPrice().getStrategy()));\n" +
                "        strategyInfoMap.forEach((key, value) -> {\n" +
                "            BigDecimal strategyAmount = promotionTotalAmountSum(value);\n" +
                "            if (strategyAmount.compareTo(BigDecimal.ZERO) <= 0) {\n" +
                "                return;\n" +
                "            }\n" +
                "\n" +
                "            StrategyInfoRsp strategyInfoRsp = new StrategyInfoRsp();\n" +
                "            strategyInfoRsp.setStrategyCode(key);\n" +
                "            strategyInfoRsp.setStrategyName(PromotionStrategyEnum.getDescByCode(key));\n" +
                "            strategyInfoRsp.setStrategyAmount(strategyAmount);\n" +
                "            result.add(strategyInfoRsp);\n" +
                "        });\n" +
                "        return result;\n" +
                "    }\n" +
                "\n" +
                "    private BigDecimal promotionTotalAmountSum(List<GoodsPriceItem> goodsPriceItems) {\n" +
                "        BigDecimal result = BigDecimal.ZERO;\n" +
                "        if (CollectionUtils.isEmpty(goodsPriceItems)) {\n" +
                "            return result;\n" +
                "        }\n" +
                "\n" +
                "        for (GoodsPriceItem goodsPriceItem : goodsPriceItems) {\n" +
                "            BigDecimal strategyAmount = goodsPriceItem.getSalePrice().subtract(goodsPriceItem.getPromotionPrice().getPromotionPrice()).multiply(BigDecimal.valueOf(goodsPriceItem.getQuantity()));\n" +
                "            result = result.add(strategyAmount);\n" +
                "        }\n" +
                "        return result;\n" +
                "    }\n" +
                "\n" +
                "}";
    }

}
