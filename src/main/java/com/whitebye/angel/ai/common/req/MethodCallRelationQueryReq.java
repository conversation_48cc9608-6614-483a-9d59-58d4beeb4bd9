package com.whitebye.angel.ai.common.req;

import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.io.Serializable;

@Data
public class MethodCallRelationQueryReq implements Serializable {

    private static final long serialVersionUID = -1750301223926462117L;

    /**
     * 调用方应用名
     */
    private String callerAppName;

    /**
     * 调用方包名
     */
    private String callerPackageName;

    /**
     * 调用方类名
     */
    private String callerClassName;

    /**
     * 调用方方法名
     */
    private String callerMethodName;

    /**
     * 被调用方应用名
     */
    private String calleeAppName;

    /**
     * 被调用方包名
     */
    private String calleePackageName;

    /**
     * 被调用方类名
     */
    private String calleeClassName;

    /**
     * 被调用方方法名
     */
    private String calleeMethodName;

    /**
     * 校验：调用方类名，调用方方法名，被调用方类名，被调用方方法名，这4个参数不能同时为空
     */
    @AssertTrue(message = "调用方类名，调用方方法名，被调用方类名，被调用方方法名，这4个参数不能同时为空")
    public boolean isValidParams() {
        return !((callerClassName == null || callerClassName.trim().isEmpty()) &&
                (callerMethodName == null || callerMethodName.trim().isEmpty()) &&
                (calleeClassName == null || calleeClassName.trim().isEmpty()) &&
                (calleeMethodName == null || calleeMethodName.trim().isEmpty()));
    }
}
