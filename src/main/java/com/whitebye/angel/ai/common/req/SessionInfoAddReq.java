package com.whitebye.angel.ai.common.req;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class SessionInfoAddReq implements Serializable {

    /**
     * 用户的唯一标识符
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 角色
     */
    private String role;

    /**
     * 当前会话数据
     */
    private String sessionData;

} 