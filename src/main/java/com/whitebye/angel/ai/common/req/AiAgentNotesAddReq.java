package com.whitebye.angel.ai.common.req;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AiAgentNotesAddReq implements Serializable {

    /**
     * 智能体ID
     */
    @NotNull(message = "智能体ID不能为空")
    private Long agentId;

    /**
     * 智能体名称
     */
    @NotBlank(message = "智能体名称不能为空")
    private String agentName;

    /**
     * 笔记标题
     */
    @NotBlank(message = "笔记标题不能为空")
    private String title;

    /**
     * 笔记内容
     */
    @NotBlank(message = "笔记内容不能为空")
    private String content;

    /**
     * 笔记分类
     */
    private String category;

    /**
     * 标签数组
     */
    private String tags;

    /**
     * 优先级(0-99,优先级最高0,优先级最低99)
     */
    private Integer priority;
} 