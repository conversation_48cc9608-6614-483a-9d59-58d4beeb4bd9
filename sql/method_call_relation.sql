-- 方法调用关系表
CREATE TABLE `method_call_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `caller_app_name` varchar(64) NOT NULL COMMENT '调用方应用名',
  `caller_package_name` varchar(256) NOT NULL COMMENT '调用方包名',
  `caller_class_name` varchar(128) NOT NULL COMMENT '调用方类名',
  `caller_method_name` varchar(128) NOT NULL COMMENT '调用方方法名',
  `callee_app_name` varchar(64) NOT NULL COMMENT '被调用方应用名',
  `callee_package_name` varchar(256) NOT NULL COMMENT '被调用方包名',
  `callee_class_name` varchar(128) NOT NULL COMMENT '被调用方类名',
  `callee_method_name` varchar(128) NOT NULL COMMENT '被调用方方法名',
  `call_type` int(11) NOT NULL COMMENT '调用类型：1=实例方法调用，2=静态方法调用，3=构造方法调用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_caller_app` (`caller_app_name`),
  KEY `idx_caller_package` (`caller_package_name`),
  KEY `idx_caller_class` (`caller_class_name`),
  KEY `idx_caller_method` (`caller_method_name`),
  KEY `idx_callee_app` (`callee_app_name`),
  KEY `idx_callee_package` (`callee_package_name`),
  KEY `idx_callee_class` (`callee_class_name`),
  KEY `idx_callee_method` (`callee_method_name`),
  KEY `idx_call_type` (`call_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='方法调用关系表';
