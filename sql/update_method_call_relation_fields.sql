-- 更新方法调用关系表字段长度
-- 解决 Data too long for column 错误

-- 增加包名字段长度到 512，以应对复杂的包名结构
ALTER TABLE `method_call_relation` 
MODIFY COLUMN `caller_package_name` varchar(512) NOT NULL COMMENT '调用方包名',
MODIFY COLUMN `callee_package_name` varchar(512) NOT NULL COMMENT '被调用方包名';

-- 增加类名和方法名字段长度到 256，以应对复杂的内部类和方法名
ALTER TABLE `method_call_relation` 
MODIFY COLUMN `caller_class_name` varchar(256) NOT NULL COMMENT '调用方类名',
MODIFY COLUMN `caller_method_name` varchar(256) NOT NULL COMMENT '调用方方法名',
MODIFY COLUMN `callee_class_name` varchar(256) NOT NULL COMMENT '被调用方类名',
MODIFY COLUMN `callee_method_name` varchar(256) NOT NULL COMMENT '被调用方方法名';

-- 查看更新后的表结构
DESCRIBE `method_call_relation`;
